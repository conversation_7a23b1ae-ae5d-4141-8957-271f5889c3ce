import { defHttp } from '/@/utils/http/axios';
import type { 
  Department, 
  DepartmentQueryParams, 
  DepartmentListResult,
  DepartmentTreeNode 
} from '/@/types/basicinfo/department';

enum Api {
  list = '/basicinfo/company/list',
  queryById = '/basicinfo/company/queryById',
  save = '/basicinfo/company/add',
  edit = '/basicinfo/company/edit',
  delete = '/basicinfo/company/delete',
  deleteBatch = '/basicinfo/company/deleteBatch',
  loadTreeRoot = '/basicinfo/company/loadTreeRoot',
  loadTreeChildren = '/basicinfo/company/loadTreeChildren',
  searchCompanyDept = '/reg/companyReg/searchCompanyDeptByPid',
}

/**
 * 获取部门列表（分页）
 * @param params 查询参数
 */
export const getDepartmentList = (params: DepartmentQueryParams) => {
  return defHttp.get<DepartmentListResult>({ url: Api.list, params });
};

/**
 * 根据父ID获取部门列表
 * @param pid 父部门ID
 * @param pageSize 页大小，默认10000
 */
export const getDepartmentsByPid = async (pid: string, pageSize: number = 10000) => {
  if (!pid) {
    return Promise.resolve([]);
  }

  try {
    // 首先尝试使用专门的部门查询接口
    const response = await defHttp.get({ 
      url: Api.searchCompanyDept, 
      params: { pid, pageSize } 
    });
    
    if (response && Array.isArray(response)) {
      return response;
    }
    
    // 如果专门接口失败，降级到通用接口
    console.warn('部门专用接口返回异常，降级到通用接口');
    const fallbackResponse = await defHttp.get<DepartmentListResult>({ 
      url: Api.list, 
      params: { 
        pid, 
        pageSize,
        orgType: '2', // 只查询部门
        delFlag: '0'  // 只查询未删除的
      } 
    });
    
    return fallbackResponse.records || [];
  } catch (error) {
    console.error('获取部门列表失败:', error);
    return [];
  }
};

/**
 * 获取部门树根节点
 * @param params 查询参数
 */
export const getDepartmentTreeRoot = (params?: any) => {
  return defHttp.get({ 
    url: Api.loadTreeRoot, 
    params: { async: true, pcode: '', ...params } 
  });
};

/**
 * 异步加载部门树子节点
 * @param params 查询参数，包含pid
 */
export const loadDepartmentTreeChildren = (params: { pid: string }) => {
  return defHttp.get({ url: Api.loadTreeChildren, params });
};

/**
 * 根据ID查询部门详情
 * @param id 部门ID
 */
export const getDepartmentById = (id: string) => {
  return defHttp.get<Department>({ url: Api.queryById, params: { id } });
};

/**
 * 保存或更新部门
 * @param params 部门数据
 * @param isUpdate 是否为更新操作
 */
export const saveOrUpdateDepartment = (params: Department, isUpdate: boolean) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url, params }, { isTransformResponse: false });
};

/**
 * 删除部门
 * @param id 部门ID
 */
export const deleteDepartment = (id: string) => {
  return defHttp.delete({ url: Api.delete, params: { id } }, { joinParamsToUrl: true });
};

/**
 * 批量删除部门
 * @param ids 部门ID数组
 */
export const batchDeleteDepartments = (ids: string[]) => {
  return defHttp.delete({ url: Api.deleteBatch, data: ids }, { joinParamsToUrl: true });
};

/**
 * 搜索部门（支持关键字搜索）
 * @param keyword 搜索关键字
 * @param companyId 所属单位ID
 * @param pageSize 返回数量限制
 */
export const searchDepartments = async (
  keyword?: string, 
  companyId?: string, 
  pageSize: number = 50
) => {
  const params: DepartmentQueryParams = {
    pageNo: 1,
    pageSize,
    orgType: '2', // 只查询部门
    delFlag: '0'  // 只查询未删除的
  };

  if (keyword && keyword.trim()) {
    params.name_LIKE = keyword.trim();
    params.departName_LIKE = keyword.trim();
    params.helpChar_LIKE = keyword.trim();
    params._searchMode = 'OR';
  }

  if (companyId) {
    params.pid = companyId;
  }

  try {
    const response = await defHttp.get<DepartmentListResult>({ url: Api.list, params });
    return response.records || [];
  } catch (error) {
    console.error('搜索部门失败:', error);
    return [];
  }
};

/**
 * 获取部门的完整路径（从根到当前节点）
 * @param departmentId 部门ID
 */
export const getDepartmentPath = async (departmentId: string): Promise<Department[]> => {
  const path: Department[] = [];
  let currentId = departmentId;

  try {
    while (currentId) {
      const dept = await getDepartmentById(currentId);
      if (dept) {
        path.unshift(dept); // 添加到数组开头
        currentId = dept.pid || '';
      } else {
        break;
      }
    }
  } catch (error) {
    console.error('获取部门路径失败:', error);
  }

  return path;
};

/**
 * 验证部门名称是否重复
 * @param name 部门名称
 * @param pid 父部门ID
 * @param excludeId 排除的部门ID（用于编辑时排除自身）
 */
export const validateDepartmentName = async (
  name: string, 
  pid?: string, 
  excludeId?: string
): Promise<boolean> => {
  try {
    const params: DepartmentQueryParams = {
      name_LIKE: name,
      orgType: '2',
      delFlag: '0'
    };

    if (pid) {
      params.pid = pid;
    }

    const response = await defHttp.get<DepartmentListResult>({ url: Api.list, params });
    const departments = response.records || [];

    // 检查是否有重复的名称（排除自身）
    const duplicates = departments.filter(dept => 
      dept.name === name && dept.id !== excludeId
    );

    return duplicates.length === 0;
  } catch (error) {
    console.error('验证部门名称失败:', error);
    return true; // 验证失败时允许通过
  }
};

/**
 * 获取部门的所有子部门（递归）
 * @param departmentId 部门ID
 */
export const getAllChildDepartments = async (departmentId: string): Promise<Department[]> => {
  const allChildren: Department[] = [];

  const loadChildren = async (pid: string) => {
    try {
      const children = await getDepartmentsByPid(pid);
      for (const child of children) {
        allChildren.push(child);
        // 如果有子节点，递归加载
        if (child.hasChild === '1' || child.hasChild === true) {
          await loadChildren(child.id);
        }
      }
    } catch (error) {
      console.error('加载子部门失败:', error);
    }
  };

  await loadChildren(departmentId);
  return allChildren;
};
