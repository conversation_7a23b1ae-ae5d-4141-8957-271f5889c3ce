/**
 * 部门基础信息
 */
export interface Department {
  /** 主键ID */
  id: string;
  /** 部门名称 */
  name?: string;
  /** 部门名称（兼容字段） */
  departName?: string;
  /** 部门简称 */
  shortName?: string;
  /** 助记码 */
  helpChar?: string;
  /** 父部门ID */
  pid?: string;
  /** 部门编码 */
  orgCode?: string;
  /** 组织类型：1-单位，2-部门 */
  orgType?: string;
  /** 是否有子节点：0-无，1-有 */
  hasChild?: string | boolean;
  /** 联系人 */
  contact?: string;
  /** 联系电话 */
  telephone?: string;
  /** 部门电话 */
  phone?: string;
  /** 地址 */
  address?: string;
  /** 邮箱 */
  email?: string;
  /** 状态 */
  status?: string;
  /** 删除标志 */
  delFlag?: string;
  /** 备注 */
  remark?: string;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 部门负责人 */
  departCharge?: string;
  /** 部门负责人姓名 */
  departChargeName?: string;
  /** 部门功能类别 */
  departFunCategory?: string;
  /** 性别限制 */
  sexLimit?: string;
  /** 日检能力 */
  maxPerDay?: number;
  /** 排序号 */
  departOrder?: number;
  /** 传真 */
  fax?: string;
  /** 描述 */
  description?: string;
  /** 移动电话 */
  mobile?: string;
  /** 办公地址 */
  officeAddress?: string;
  /** 邮政编码 */
  postcode?: string;
  /** 部门类型 */
  departType?: string;
  /** 部门级别 */
  departLevel?: number;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 部门树节点
 */
export interface DepartmentTreeNode {
  /** 节点键值 */
  key: string;
  /** 节点值 */
  value: string;
  /** 节点标题 */
  title: string;
  /** 是否为叶子节点 */
  isLeaf?: boolean;
  /** 子节点 */
  children?: DepartmentTreeNode[];
  /** 组织类型：1-单位，2-部门 */
  orgType?: string;
  /** 父节点ID */
  pid?: string;
  /** 是否有子节点 */
  hasChild?: string | boolean;
  /** 原始数据 */
  data?: Department;
}

/**
 * 部门选择器选项
 */
export interface DepartmentSelectOption {
  /** 值 */
  value: string;
  /** 标签 */
  label: string;
  /** 原始数据 */
  data: Department;
}

/**
 * 部门查询参数
 */
export interface DepartmentQueryParams {
  /** 页码 */
  pageNo?: number;
  /** 页大小 */
  pageSize?: number;
  /** 父部门ID */
  pid?: string;
  /** 部门名称（模糊查询） */
  name_LIKE?: string;
  /** 部门名称（模糊查询，兼容字段） */
  departName_LIKE?: string;
  /** 助记码（模糊查询） */
  helpChar_LIKE?: string;
  /** 组织类型 */
  orgType?: string;
  /** 状态 */
  status?: string;
  /** 删除标志 */
  delFlag?: string;
  /** 搜索模式 */
  _searchMode?: string;
}

/**
 * 部门列表响应结果
 */
export interface DepartmentListResult {
  /** 记录列表 */
  records: Department[];
  /** 总数 */
  total: number;
  /** 当前页 */
  current: number;
  /** 页大小 */
  size: number;
}

/**
 * 部门树选择器配置
 */
export interface DepartmentTreeSelectConfig {
  /** 是否允许清空 */
  allowClear?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 占位符 */
  placeholder?: string;
  /** 是否显示搜索 */
  showSearch?: boolean;
  /** 是否只显示部门（不显示单位） */
  onlyDepartments?: boolean;
  /** 最大高度 */
  maxHeight?: string;
  /** 是否支持异步加载 */
  loadData?: boolean;
}

/**
 * 部门树选择器事件参数
 */
export interface DepartmentTreeSelectEventParams {
  /** 选中的值 */
  value?: string;
  /** 选中的节点 */
  node?: DepartmentTreeNode;
  /** 额外信息 */
  extra?: any;
}
