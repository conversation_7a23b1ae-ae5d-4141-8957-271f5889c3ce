<template>
  <a-modal
    v-model:open="visible"
    :title="isEditMode ? '编辑字段显示配置' : '创建字段显示配置'"
    width="1000px"
    :confirm-loading="confirmLoading"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <div class="config-container">
        <!-- 配置基本信息 -->
        <a-card size="small" title="配置信息" class="config-info-card">
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="配置名称" v-bind="validateInfos.configName">
                <a-input v-model:value="formData.configName" placeholder="请输入配置名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="表单类型" v-bind="validateInfos.formType">
                <a-select v-model:value="formData.formType" placeholder="请选择表单类型" @change="handleFormTypeChange">
                  <a-select-option value="customer_reg">客户登记表单</a-select-option>
                  <!--                  <a-select-option value="company_reg">单位登记表单</a-select-option>
                  <a-select-option value="exam_config">体检配置表单</a-select-option>-->
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否启用">
                <a-switch v-model:checked="formData.isActive" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <!-- 字段显示配置区域 -->
        <a-card size="small" title="字段显示位置配置" class="fields-config-card">
          <template #extra>
            <a-space>
              <a-button size="small" @click="addNewField" type="primary"> <Icon icon="ant-design:plus-outlined" /> 新增字段 </a-button>
              <a-button size="small" @click="loadDefaultConfig"> <Icon icon="ant-design:reload-outlined" /> 加载默认配置 </a-button>
              <a-button size="small" @click="selectAll"> <Icon icon="ant-design:check-outlined" /> 全选 </a-button>
              <a-button size="small" @click="selectNone"> <Icon icon="ant-design:close-outlined" /> 全不选 </a-button>
            </a-space>
          </template>

          <!-- 字段配置表格 -->
          <div class="fields-table">
            <div class="field-group-title">
              配置字段显示位置：
              <span style="color: #666; font-size: 12px; margin-left: 10px"> (当前共 {{ formData.fields.length }} 个字段) </span>
            </div>
            <a-table
              :columns="fieldColumns"
              :data-source="formData.fields"
              :pagination="false"
              size="small"
              row-key="fieldKey"
              :scroll="{ y: 400 }"
              @change="handleTableChange"
            >
              <!-- 字段标识列 -->
              <template #fieldKey="{ record }">
                <a-input
                  v-model:value="record.fieldKey"
                  size="small"
                  placeholder="请输入字段标识"
                  @change="handleFieldKeyChange(record)"
                />
              </template>

              <!-- 字段名称列 -->
              <template #fieldName="{ record }">
                <a-input
                  v-model:value="record.fieldName"
                  size="small"
                  placeholder="请输入字段名称"
                  @change="handleFieldNameChange(record)"
                />
              </template>

              <!-- 所属分类列 -->
              <template #groupName="{ record }">
                <a-select
                  v-model:value="record.groupName"
                  size="small"
                  style="width: 100%"
                  placeholder="请选择分类"
                  @change="handleGroupNameChange(record)"
                  :options="groupOptions"
                />
              </template>

              <!-- 显示位置列 -->
              <template #displayLocation="{ record }">
                <a-select v-model:value="record.displayLocation" size="small" style="width: 120px" @change="handleLocationChange(record)">
                  <a-select-option value="outside">
                    <a-tag color="green">外部区域</a-tag>
                  </a-select-option>
                  <a-select-option value="collapse">
                    <a-tag color="blue">折叠区域</a-tag>
                  </a-select-option>
                  <a-select-option value="hidden">
                    <a-tag color="red">隐藏</a-tag>
                  </a-select-option>
                </a-select>
              </template>

              <!-- 是否可见列 -->
              <template #isVisible="{ record }">
                <a-switch v-model:checked="record.isVisible" size="small" @change="handleVisibilityChange(record)" />
              </template>

              <!-- 排序列 -->
              <template #sortOrder="{ record }">
                <a-input-number v-model:value="record.sortOrder" size="small" :min="0" :max="999" style="width: 80px" />
              </template>

              <!-- 操作列 -->
              <template #action="{ record }">
                <a-space size="small">
                  <a-button type="link" size="small" @click="moveUp(record)" :disabled="isFirstInGroup(record)" title="上移">
                    <Icon icon="ant-design:arrow-up-outlined" />
                  </a-button>
                  <a-button type="link" size="small" @click="moveDown(record)" :disabled="isLastInGroup(record)" title="下移">
                    <Icon icon="ant-design:arrow-down-outlined" />
                  </a-button>
                  <a-button type="link" size="small" danger @click="deleteField(record)" title="删除">
                    <Icon icon="ant-design:delete-outlined" />
                  </a-button>
                </a-space>
              </template>
            </a-table>
          </div>
        </a-card>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch } from 'vue';
  import { Form, message, Modal } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import {
    FormDisplayConfig,
    FieldDisplayConfig,
    FieldDisplayLocation,
    saveFormDisplayConfig,
    updateFormDisplayConfig,
    FORM_TYPES,
    migrateFieldConfig,
    validateFieldConfig,
    getDefaultFieldConfig,
  } from '../FormFieldConfig.api';

  const emit = defineEmits(['register', 'success']);

  const visible = ref(false);
  const loading = ref(false);
  const confirmLoading = ref(false);
  const showDebugInfo = ref(import.meta.env.DEV); // 开发环境显示调试信息
  const isEditMode = ref(false); // 是否为编辑模式

  const formData = reactive<FormDisplayConfig>({
    configName: '',
    centerId: '',
    centerName: '',
    formType: 'customer_reg',
    isActive: true,
    fields: [],
  });

  // 表单验证
  const useForm = Form.useForm;
  const rules = reactive({
    configName: [{ required: true, message: '请输入配置名称' }],
    formType: [{ required: true, message: '请选择表单类型' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, rules);

  // 分组选项
  const groupOptions = ref([
    { label: '基本信息', value: '基本信息' },
    { label: '详细信息', value: '详细信息' },
    { label: '联系信息', value: '联系信息' },
    { label: '地址信息', value: '地址信息' },
    { label: '健康信息', value: '健康信息' },
    { label: '证件信息', value: '证件信息' },
    { label: '职业信息', value: '职业信息' },
    { label: '单位信息', value: '单位信息' },
    { label: '标识信息', value: '标识信息' },
    { label: '财务信息', value: '财务信息' },
    { label: '原检信息', value: '原检信息' },
    { label: '其他信息', value: '其他信息' },
  ]);

  // 表格列定义
  const fieldColumns = [
    {
      title: '字段标识',
      dataIndex: 'fieldKey',
      key: 'fieldKey',
      width: 120,
      slots: { customRender: 'fieldKey' },
    },
    {
      title: '字段名称',
      dataIndex: 'fieldName',
      key: 'fieldName',
      width: 120,
      slots: { customRender: 'fieldName' },
    },
    {
      title: '所属分类',
      dataIndex: 'groupName',
      key: 'groupName',
      width: 120,
      slots: { customRender: 'groupName' },
    },
    {
      title: '显示位置',
      dataIndex: 'displayLocation',
      key: 'displayLocation',
      width: 140,
      slots: { customRender: 'displayLocation' },
    },
    {
      title: '是否可见',
      dataIndex: 'isVisible',
      key: 'isVisible',
      width: 80,
      align: 'center',
      slots: { customRender: 'isVisible' },
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
      align: 'center',
      slots: { customRender: 'sortOrder' },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      align: 'center',
      slots: { customRender: 'action' },
    },
  ];

  // 表单类型变化处理
  const handleFormTypeChange = (formType: string) => {
    console.log('表单类型变化:', formType);
    loadDefaultConfig();
  };

  // 表格变化处理
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    console.log('表格变化:', { pagination, filters, sorter });
    console.log('当前表格数据源:', formData.fields);
  };

  // 显示位置变化处理
  const handleLocationChange = (record: FieldDisplayConfig) => {
    // 根据显示位置自动设置可见性
    if (record.displayLocation === FieldDisplayLocation.HIDDEN) {
      record.isVisible = false;
    } else {
      record.isVisible = true;
    }
  };

  // 可见性变化处理
  const handleVisibilityChange = (record: FieldDisplayConfig) => {
    // 如果设置为不可见，自动设置为隐藏
    if (!record.isVisible) {
      record.displayLocation = FieldDisplayLocation.HIDDEN;
    } else if (record.displayLocation === FieldDisplayLocation.HIDDEN) {
      // 如果从隐藏状态设置为可见，默认放到折叠区域
      record.displayLocation = FieldDisplayLocation.COLLAPSE;
    }
  };

  // 字段标识变化处理
  const handleFieldKeyChange = (record: FieldDisplayConfig) => {
    console.log('字段标识变化:', record.fieldKey);
    // 可以在这里添加字段标识唯一性验证
    validateFieldKey(record);
  };

  // 字段名称变化处理
  const handleFieldNameChange = (record: FieldDisplayConfig) => {
    console.log('字段名称变化:', record.fieldName);
    // 可以在这里添加字段名称验证
  };

  // 所属分类变化处理
  const handleGroupNameChange = (record: FieldDisplayConfig) => {
    console.log('所属分类变化:', record.groupName);
  };

  // 验证字段标识唯一性
  const validateFieldKey = (currentRecord: FieldDisplayConfig) => {
    const duplicates = formData.fields.filter(
      field => field.fieldKey === currentRecord.fieldKey && field !== currentRecord
    );

    if (duplicates.length > 0) {
      message.warning(`字段标识 "${currentRecord.fieldKey}" 已存在，请使用唯一的标识`);
      // 可以考虑自动生成唯一标识
      currentRecord.fieldKey = generateUniqueFieldKey(currentRecord.fieldKey);
    }
  };

  // 生成唯一字段标识
  const generateUniqueFieldKey = (baseKey: string): string => {
    let counter = 1;
    let newKey = `${baseKey}_${counter}`;

    while (formData.fields.some(field => field.fieldKey === newKey)) {
      counter++;
      newKey = `${baseKey}_${counter}`;
    }

    return newKey;
  };

  // 新增字段
  const addNewField = () => {
    const newFieldKey = generateUniqueFieldKey('newField');
    const maxSortOrder = Math.max(...formData.fields.map(f => f.sortOrder || 0), 0);

    const newField: FieldDisplayConfig = {
      fieldKey: newFieldKey,
      fieldName: '新字段',
      groupName: '基本信息',
      displayLocation: FieldDisplayLocation.OUTSIDE,
      isVisible: true,
      sortOrder: maxSortOrder + 1,
    };

    formData.fields.push(newField);
    message.success('新字段已添加');
  };

  // 删除字段
  const deleteField = (record: FieldDisplayConfig) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除字段 "${record.fieldName}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        const index = formData.fields.findIndex(field => field === record);
        if (index > -1) {
          formData.fields.splice(index, 1);
          message.success('字段已删除');
        }
      },
    });
  };

  // 检查是否是分组中的第一个
  const isFirstInGroup = (record: FieldDisplayConfig) => {
    const sameGroupFields = formData.fields.filter((f) => f.groupName === record.groupName);
    return sameGroupFields[0]?.fieldKey === record.fieldKey;
  };

  // 检查是否是分组中的最后一个
  const isLastInGroup = (record: FieldDisplayConfig) => {
    const sameGroupFields = formData.fields.filter((f) => f.groupName === record.groupName);
    return sameGroupFields[sameGroupFields.length - 1]?.fieldKey === record.fieldKey;
  };

  // 上移字段
  const moveUp = (record: FieldDisplayConfig) => {
    const index = formData.fields.findIndex((f) => f.fieldKey === record.fieldKey);
    if (index > 0) {
      const temp = formData.fields[index];
      formData.fields[index] = formData.fields[index - 1];
      formData.fields[index - 1] = temp;

      // 更新排序值
      updateSortOrder();
    }
  };

  // 下移字段
  const moveDown = (record: FieldDisplayConfig) => {
    const index = formData.fields.findIndex((f) => f.fieldKey === record.fieldKey);
    if (index < formData.fields.length - 1) {
      const temp = formData.fields[index];
      formData.fields[index] = formData.fields[index + 1];
      formData.fields[index + 1] = temp;

      // 更新排序值
      updateSortOrder();
    }
  };

  // 更新排序值
  const updateSortOrder = () => {
    formData.fields.forEach((field, index) => {
      field.sortOrder = index + 1;
    });
  };

  // 全选
  const selectAll = () => {
    formData.fields.forEach((field) => {
      field.isVisible = true;
    });
  };

  // 全不选
  const selectNone = () => {
    formData.fields.forEach((field) => {
      field.isVisible = false;
    });
  };

  // 加载默认配置
  const loadDefaultConfig = () => {
    console.log('=== FormFieldConfigModal.loadDefaultConfig ===');
    console.log('当前表单类型:', formData.formType);

    if (formData.formType === FORM_TYPES.CUSTOMER_REG) {
      // 使用新的默认配置函数
      formData.fields = getDefaultFieldConfig();
      console.log('获取到的默认配置:', formData.fields);
      console.log('默认配置字段数量:', formData.fields.length);

      // 确保配置完整性
      formData.fields = migrateFieldConfig(formData.fields);
      console.log('迁移后的配置:', formData.fields);

      // 验证配置
      const validation = validateFieldConfig(formData.fields);
      console.log('配置验证结果:', validation);

      if (!validation.isValid) {
        console.warn('默认配置验证失败:', validation.errors);
        message.warning('默认配置存在问题，请检查字段配置');
      }

      if (validation.warnings.length > 0) {
        console.warn('默认配置警告:', validation.warnings);
      }

      message.success('已加载默认字段配置');
    } else {
      formData.fields = [];
      message.info('该表单类型暂无默认配置');
    }
  };

  // 保存配置
  const handleSave = async () => {
    try {
      await validate();
      confirmLoading.value = true;

      // 验证字段配置
      const validation = validateFieldConfig(formData.fields);
      if (!validation.isValid) {
        message.error('字段配置验证失败: ' + validation.errors.join(', '));
        return;
      }

      if (validation.warnings.length > 0) {
        console.warn('字段配置警告:', validation.warnings);
      }

      // 确保配置完整性
      formData.fields = migrateFieldConfig(formData.fields);

      // 确保所有字段都有有效的排序值
      formData.fields.forEach((field, index) => {
        if (!field.sortOrder || field.sortOrder <= 0) {
          field.sortOrder = index + 1;
        }
        if (field.isRequired === undefined) {
          field.isRequired = false;
        }
        if (!field.groupName) {
          field.groupName = '默认分组';
        }
      });

      // 准备保存数据
      const saveData = {
        ...formData,
        fields: formData.fields.map((field, index) => {
          const cleanField = {
            fieldKey: field.fieldKey,
            fieldName: field.fieldName,
            isVisible: field.isVisible,
            displayLocation: field.displayLocation,
            groupName: field.groupName || '',
            sortOrder: field.sortOrder || index + 1,
            isRequired: field.isRequired || false,
          };

          // 编辑模式：保留字段ID，后端会进行UPDATE操作
          // 创建模式：不包含ID，后端会进行INSERT操作
          if (isEditMode.value && field.id) {
            cleanField.id = field.id;
          }

          return cleanField;
        }),
      };

      console.log('=== 准备保存配置 ===');
      console.log('操作模式:', isEditMode.value ? '编辑' : '创建');
      console.log('是否有配置ID:', !!formData.id);
      console.log('原始数据:', formData);
      console.log('保存数据:', saveData);
      console.log('字段数量:', saveData.fields.length);

      // 验证保存数据的完整性
      if (!saveData.configName || saveData.configName.trim() === '') {
        message.error('配置名称不能为空');
        return;
      }

      if (!saveData.fields || saveData.fields.length === 0) {
        message.error('至少需要配置一个字段');
        return;
      }

      // 检查字段数据完整性
      const invalidFields = saveData.fields.filter((field) => !field.fieldKey || !field.fieldName || !field.displayLocation);

      if (invalidFields.length > 0) {
        console.error('发现无效字段:', invalidFields);
        message.error('存在无效的字段配置，请检查');
        return;
      }

      console.log(`数据验证通过，开始${isEditMode.value ? '更新' : '新增'}配置...`);

      try {
        // 根据模式选择正确的API
        let result;
        if (isEditMode.value && formData.id) {
          // 编辑模式：使用更新API
          console.log('编辑模式：调用更新API');
          result = await updateFormDisplayConfig(saveData);
        } else {
          // 创建模式：使用新增API
          console.log('创建模式：调用新增API');
          result = await saveFormDisplayConfig(saveData);
        }

        console.log(`API${isEditMode.value ? '更新' : '保存'}成功，返回结果:`, result);
        message.success(`配置${isEditMode.value ? '更新' : '保存'}成功`);
      } catch (apiError) {
        console.error('API保存失败，详细错误信息:', apiError);
        console.error('错误响应:', apiError.response);
        console.error('错误消息:', apiError.message);

        // 显示具体的错误信息
        let errorMessage = '保存失败';
        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        } else if (apiError.message) {
          errorMessage = apiError.message;
        }

        console.warn('API保存失败，尝试本地存储:', errorMessage);

        // 如果API失败，保存到本地存储作为备份
        try {
          localStorage.setItem('fieldDisplayConfig', JSON.stringify(formData));
          message.warning('服务器保存失败，已保存到本地存储');
        } catch (localError) {
          console.error('本地存储也失败:', localError);
          message.error('保存失败，请稍后重试');
          return;
        }
      }

      emit('success');
      handleCancel();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败: ' + (error.message || '未知错误'));
    } finally {
      confirmLoading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    console.log('=== 取消配置弹窗 ===');
    visible.value = false;
    resetFields();
    // 注意：这里清空 fields 可能会影响下次打开时的数据显示
    // 如果需要保持数据，可以考虑不清空或者在 open 时重新赋值
    formData.fields = [];
    console.log('已清空表单数据');
  };

  // 打开弹窗
  const open = (config?: FormDisplayConfig, mode: 'create' | 'edit' = 'create') => {
    console.log('=== FormFieldConfigModal.open ===');
    console.log('接收到的配置参数:', config);
    console.log('打开模式:', mode);

    visible.value = true;
    isEditMode.value = mode === 'edit';

    if (config && mode === 'edit') {
      console.log('编辑模式：使用传入的配置数据');
      console.log('配置字段数量:', config.fields?.length || 0);
      console.log('配置字段详情:', config.fields);

      // 编辑模式：保留所有数据包括ID
      Object.assign(formData, {
        id: config.id,
        configName: config.configName || '',
        centerId: config.centerId || '',
        centerName: config.centerName || '',
        formType: config.formType || 'customer_reg',
        isActive: config.isActive !== undefined ? config.isActive : true,
        fields: JSON.parse(JSON.stringify(config.fields || [])),
      });

      console.log('编辑模式赋值后的formData:', formData);
    } else {
      console.log('创建模式：基于现有配置创建新配置');

      if (config) {
        // 创建模式：基于现有配置但清除ID，创建新配置
        Object.assign(formData, {
          id: undefined, // 清除ID，表示新建
          configName: `${config.configName || '新配置'}_副本_${Date.now()}`,
          centerId: config.centerId || '',
          centerName: config.centerName || '',
          formType: config.formType || 'customer_reg',
          isActive: true,
          fields: config.fields
            ? config.fields.map((field) => ({
                // 清除字段ID，保留配置信息
                fieldKey: field.fieldKey,
                fieldName: field.fieldName,
                isVisible: field.isVisible,
                displayLocation: field.displayLocation,
                groupName: field.groupName,
                sortOrder: field.sortOrder,
                isRequired: field.isRequired || false,
              }))
            : [],
        });
      } else {
        // 完全新建：加载默认配置
        loadDefaultConfig();
      }
    }

    console.log('最终formData:', formData);
    console.log('formData.fields数量:', formData.fields.length);
  };

  // 监控字段数据变化
  watch(
    () => formData.fields,
    (newFields) => {
      console.log('=== formData.fields 变化 ===');
      console.log('新的字段数据:', newFields);
      console.log('字段数量:', newFields?.length || 0);
      if (newFields && newFields.length > 0) {
        console.log('字段详情:');
        newFields.forEach((field, index) => {
          console.log(`${index + 1}. ${field.fieldName} (${field.fieldKey}) - ${field.displayLocation} - 可见:${field.isVisible}`);
        });
      }
    },
    { deep: true, immediate: true }
  );

  // 暴露方法
  defineExpose({
    open,
  });
</script>

<style scoped>
  .config-container {
    max-height: 600px;
    overflow-y: auto;
  }

  .config-info-card {
    margin-bottom: 16px;
  }

  .debug-info-card {
    margin-bottom: 16px;
    background: #f9f9f9;
  }

  .fields-config-card {
    margin-bottom: 16px;
  }

  .fields-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .fields-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
  }

  .field-group-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: #f0f9ff;
    border-left: 3px solid #1890ff;
    border-radius: 4px;
  }

  .field-item {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 12px;
    background: #fff;
    transition: all 0.2s;
  }

  .field-item:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }

  .field-enabled {
    background: #f6ffed;
    border-color: #b7eb8f;
  }

  .field-header {
    display: flex;
    align-items: center;
  }

  .field-info {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .field-checkbox {
    margin: 0;
  }

  .field-name {
    font-weight: 500;
    color: #333;
    flex: 1;
  }

  /* 表格相关样式 */
  .ant-table {
    .ant-table-tbody > tr > td {
      padding: 8px;
      vertical-align: middle;
    }
  }

  /* 可编辑输入框样式 */
  .ant-input {
    border: 1px solid #d9d9d9;
    transition: border-color 0.3s;
  }

  .ant-input:hover {
    border-color: #40a9ff;
  }

  .ant-input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .ant-select {
    width: 100%;
  }

  /* 操作按钮样式 */
  .ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1;
  }

  .ant-btn-link[disabled] {
    color: #d9d9d9;
  }

  /* 表格行样式优化 */
  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f5f5f5;
  }

  /* 新增字段按钮样式 */
  .ant-btn-primary {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-btn-primary:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }

  /* 位置标签样式 */
  .location-tag {
    font-size: 12px;
  }

  /* 排序输入框样式 */
  .sort-input {
    width: 60px;
  }
</style>
