# 所属预约自动填充单位信息功能说明

## 功能概述

当用户在客户登记表单中选择"所属预约"后，系统会自动调用后台接口获取预约关联的单位信息，并智能地填充到表单的单位相关字段中，进一步触发单位ID的补充逻辑，如自动加载部门列表等。

## 功能特点

### 1. 智能填充策略
- **自动检测**：系统会自动检测预约中是否包含单位信息
- **智能判断**：比较预约中的单位信息与当前表单中的单位信息
- **用户确认**：当检测到信息不一致时，会询问用户是否覆盖

### 2. 多字段联动填充
- **基本单位信息**：单位ID、单位名称、行业
- **联系信息**：联系电话、地址、邮箱（仅在表单为空时填充）
- **关联数据**：自动触发部门列表加载

### 3. 用户体验优化
- **加载提示**：显示"正在加载预约信息..."的提示
- **操作反馈**：成功填充后显示确认消息
- **错误处理**：友好的错误提示和异常处理

## 技术实现

### 核心函数

#### 1. handleCompanyRegChange()
主要的预约变化处理函数，负责：
- 并行加载团队列表和预约详情
- 调用单位信息自动填充逻辑
- 错误处理和用户提示

#### 2. handleCompanyInfoAutoFill()
单位信息自动填充的核心逻辑：
- 检查预约中是否包含单位信息
- 调用确认逻辑判断是否需要填充
- 执行实际的填充操作

#### 3. checkAndConfirmCompanyUpdate()
智能判断和用户确认逻辑：
- 比较当前单位信息与预约中的单位信息
- 当信息不一致时弹出确认对话框
- 返回用户的选择结果

#### 4. fillCompanyInfo()
执行实际的单位信息填充：
- 填充基本单位信息（ID、名称、行业）
- 调用其他相关信息填充
- 触发单位ID变化的相关逻辑

#### 5. fillAdditionalCompanyInfo()
填充其他相关的单位信息：
- 联系电话（仅在表单为空时）
- 地址（仅在表单为空时）
- 邮箱（仅在表单为空时）

### 数据流程

```
用户选择所属预约
    ↓
调用 handleCompanyRegChange()
    ↓
并行加载团队列表和预约详情
    ↓
调用 handleCompanyInfoAutoFill()
    ↓
检查预约中是否有单位信息
    ↓
调用 checkAndConfirmCompanyUpdate()
    ↓
比较当前单位信息与预约单位信息
    ↓
如果不一致，弹出确认对话框
    ↓
用户选择是否覆盖
    ↓
如果确认，调用 fillCompanyInfo()
    ↓
填充基本单位信息
    ↓
调用 fillAdditionalCompanyInfo()
    ↓
填充其他相关信息
    ↓
触发单位ID变化逻辑
    ↓
显示成功提示
```

## 使用场景

### 场景1：新建客户登记
1. 用户打开新建客户登记表单
2. 选择所属预约
3. 系统自动填充预约关联的单位信息
4. 用户继续填写其他信息

### 场景2：编辑已有客户
1. 用户编辑已有的客户登记
2. 更改所属预约
3. 系统检测到单位信息不一致
4. 弹出确认对话框询问是否覆盖
5. 用户选择后系统执行相应操作

### 场景3：预约无单位信息
1. 用户选择所属预约
2. 系统检测到预约中没有单位信息
3. 不执行任何填充操作
4. 在日志中记录相关信息

## 配置说明

### API接口要求
- `getCompanyRegById()` 接口必须返回完整的单位信息
- 返回数据结构应包含 `company` 对象
- `company` 对象应包含：`name`、`workIndustry`、`telephone`、`address`、`email` 等字段

### 表单字段映射
```javascript
// 预约数据 -> 表单字段
companyRegData.companyId -> formData.companyId
companyRegData.company.name -> formData.companyName
companyRegData.company.workIndustry -> formData.industry
companyRegData.company.telephone -> formData.phone (仅在为空时)
companyRegData.company.address -> formData.address (仅在为空时)
companyRegData.company.email -> formData.email (仅在为空时)
```

## 测试验证

### 测试页面
提供了专门的测试页面：`src/views/reg/test/CompanyAutoFillTest.vue`

### 测试用例
1. **正常填充测试**：选择预约后自动填充单位信息
2. **冲突处理测试**：当前有单位信息时的覆盖确认
3. **空数据测试**：预约中没有单位信息的处理
4. **错误处理测试**：API调用失败的处理

### 测试数据
测试页面包含3个测试预约：
- 测试预约1：科技公司体检
- 测试预约2：制造企业体检  
- 测试预约3：医疗机构体检

## 日志和调试

### 控制台日志
系统会在控制台输出详细的操作日志：
- 预约选择和变化
- API调用状态
- 单位信息比较结果
- 填充操作详情
- 错误信息

### 日志级别
- `INFO`：一般信息，如操作开始、完成等
- `SUCCESS`：成功操作，如填充完成
- `WARN`：警告信息，如数据不一致
- `ERROR`：错误信息，如API调用失败

## 注意事项

1. **权限控制**：确保用户有访问预约信息的权限
2. **数据完整性**：预约中的单位信息可能不完整，需要做好空值处理
3. **用户体验**：避免频繁的确认对话框，只在真正需要时弹出
4. **性能考虑**：使用并行加载提高响应速度
5. **错误恢复**：API调用失败时不应影响其他功能的正常使用

## 扩展功能

### 可能的扩展方向
1. **批量填充**：支持批量客户登记时的单位信息填充
2. **历史记录**：记录单位信息的变更历史
3. **智能推荐**：根据历史数据推荐可能的单位信息
4. **字段映射配置**：允许用户自定义字段映射关系

### 配置化改进
1. **填充策略配置**：允许配置哪些字段需要自动填充
2. **确认策略配置**：配置什么情况下需要用户确认
3. **默认值配置**：为不同类型的预约配置默认的单位信息

## 总结

所属预约自动填充单位信息功能大大提升了用户的操作效率，减少了重复输入，同时通过智能判断和用户确认机制，确保了数据的准确性和用户的操作自主权。该功能的实现充分考虑了用户体验、数据完整性和系统稳定性，是一个实用且可靠的功能增强。
