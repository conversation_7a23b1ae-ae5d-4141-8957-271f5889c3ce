# 可编辑字段配置功能说明

## 功能概述

字段配置功能已升级为完全可编辑模式，用户可以直接在配置表格中编辑字段标识、字段名称和所属分类，并支持新增和删除字段。

## 主要改进

### 1. 可编辑字段
- **字段标识**：可直接在表格中编辑，系统会自动验证唯一性
- **字段名称**：可直接在表格中编辑，支持中文和英文
- **所属分类**：通过下拉选择框选择，支持预设分类

### 2. 表格列重新设计
原来的表格结构：
```
字段名称 | 字段标识 | 显示位置 | 是否可见 | 排序 | 操作
```

新的表格结构：
```
字段标识 | 字段名称 | 所属分类 | 显示位置 | 是否可见 | 排序 | 操作
```

### 3. 新增功能
- **新增字段**：点击"新增字段"按钮可添加新的字段配置
- **删除字段**：每行都有删除按钮，可删除不需要的字段
- **字段验证**：自动验证字段标识的唯一性

## 详细功能说明

### 字段标识编辑
- 直接在表格中点击字段标识列的输入框进行编辑
- 系统会自动检查标识的唯一性
- 如果输入重复的标识，系统会提示并自动生成唯一标识

### 字段名称编辑
- 直接在表格中点击字段名称列的输入框进行编辑
- 支持中文、英文和数字
- 建议使用简洁明了的名称

### 所属分类选择
- 通过下拉选择框选择字段所属的分类
- 预设分类包括：
  - 基本信息
  - 详细信息
  - 联系信息
  - 地址信息
  - 健康信息
  - 证件信息
  - 职业信息
  - 单位信息
  - 标识信息
  - 财务信息
  - 原检信息
  - 其他信息

### 显示位置配置
- **外部**：字段在表单的外部区域显示，始终可见
- **折叠**：字段在折叠区域显示，需要展开才能看到
- **隐藏**：字段完全隐藏，不在表单中显示

### 可见性控制
- 勾选"是否可见"复选框控制字段是否显示
- 如果设置为不可见，显示位置会自动设置为"隐藏"
- 如果从隐藏状态设置为可见，显示位置会自动设置为"折叠"

### 排序功能
- 通过排序输入框设置字段的显示顺序
- 数值越小，显示位置越靠前
- 支持上移、下移按钮快速调整顺序

## 操作指南

### 1. 编辑现有字段
1. 打开字段配置弹窗
2. 直接在表格中点击要编辑的单元格
3. 修改字段标识、名称或分类
4. 调整显示位置和可见性
5. 点击"保存配置"按钮

### 2. 新增字段
1. 点击表格上方的"新增字段"按钮
2. 系统会自动添加一个新字段行
3. 编辑字段标识、名称和分类
4. 设置显示位置和可见性
5. 调整排序顺序
6. 点击"保存配置"按钮

### 3. 删除字段
1. 找到要删除的字段行
2. 点击操作列中的删除按钮（红色垃圾桶图标）
3. 在确认对话框中点击"确定"
4. 字段将从配置中移除

### 4. 调整字段顺序
方法一：使用上移/下移按钮
1. 点击操作列中的上移或下移按钮
2. 字段会在同分组内移动位置

方法二：直接修改排序数值
1. 在排序列的输入框中输入新的数值
2. 数值越小，显示位置越靠前

## 数据格式

字段配置的数据格式如下：
```javascript
{
  fieldKey: 'fieldIdentifier',      // 字段标识（唯一）
  fieldName: '字段显示名称',         // 字段名称
  groupName: '所属分类',            // 所属分类
  displayLocation: 'outside',       // 显示位置：outside/collapse/hidden
  isVisible: true,                  // 是否可见
  sortOrder: 1                      // 排序顺序
}
```

## 验证规则

### 字段标识验证
- 必须唯一，不能重复
- 建议使用英文和数字，避免特殊字符
- 如果输入重复标识，系统会自动生成唯一标识

### 字段名称验证
- 不能为空
- 建议使用简洁明了的中文名称

### 分类验证
- 必须从预设分类中选择
- 如果需要新的分类，请联系开发人员添加

## 测试功能

为了方便测试和验证功能，提供了测试页面：
- 文件位置：`src/views/reg/test/EditableFieldConfigTest.vue`
- 功能：可视化测试字段配置的编辑功能
- 包含测试数据和配置预览

## 注意事项

1. **保存配置**：修改后务必点击"保存配置"按钮，否则修改不会生效
2. **字段标识唯一性**：字段标识必须唯一，系统会自动验证
3. **分类选择**：建议根据字段的实际用途选择合适的分类
4. **显示位置**：合理安排字段的显示位置，避免外部区域字段过多
5. **排序顺序**：保持合理的排序顺序，便于用户填写表单

## 技术实现

### 主要修改文件
- `src/views/reg/components/FormFieldConfigModal.vue` - 主要配置弹窗组件
- 新增可编辑表格列
- 新增字段验证逻辑
- 新增新增/删除字段功能

### 新增功能函数
- `handleFieldKeyChange()` - 字段标识变化处理
- `handleFieldNameChange()` - 字段名称变化处理
- `handleGroupNameChange()` - 所属分类变化处理
- `validateFieldKey()` - 字段标识唯一性验证
- `generateUniqueFieldKey()` - 生成唯一字段标识
- `addNewField()` - 新增字段
- `deleteField()` - 删除字段

### 数据结构优化
- 表格列重新设计，突出可编辑性
- 增加分组选项配置
- 优化样式和交互体验

## 后续扩展

1. **分类管理**：支持用户自定义分类
2. **字段模板**：提供常用字段模板
3. **批量操作**：支持批量修改字段属性
4. **导入导出**：支持配置的导入导出功能
5. **权限控制**：根据用户权限控制编辑功能
