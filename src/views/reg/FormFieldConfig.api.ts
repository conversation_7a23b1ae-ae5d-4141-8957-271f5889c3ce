import { defHttp } from '/@/utils/http/axios';

/**
 * 字段显示位置枚举
 */
export enum FieldDisplayLocation {
  OUTSIDE = 'outside', // 外部区域（固定显示）
  COLLAPSE = 'collapse', // 折叠区域（可展开）
  HIDDEN = 'hidden', // 隐藏（不显示）
}

/**
 * 字段显示配置
 */
export interface FieldDisplayConfig {
  id?: string; // 数据库ID（可选，保存时会被移除）
  fieldKey: string; // 字段标识
  fieldName: string; // 字段显示名称
  isVisible: boolean; // 是否可见（向后兼容）
  displayLocation: FieldDisplayLocation; // 显示位置
  groupName?: string; // 分组名称（可选）
  sortOrder?: number; // 排序顺序
  configId?: string; // 配置ID（可选）
  isRequired?: boolean; // 是否必填（可选）
  createTime?: string; // 创建时间（可选）
  updateTime?: string; // 更新时间（可选）
}

/**
 * 简化的表单显示配置
 */
export interface FormDisplayConfig {
  id?: string;
  configName: string; // 配置名称
  centerId: string; // 体检中心ID
  centerName: string; // 体检中心名称
  formType: string; // 表单类型：customer_reg/company_reg等
  isActive: boolean; // 是否启用
  fields: FieldDisplayConfig[]; // 字段显示配置列表
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

/**
 * 预定义的表单类型
 */
export const FORM_TYPES = {
  CUSTOMER_REG: 'customer_reg',
  COMPANY_REG: 'company_reg',
  EXAM_CONFIG: 'exam_config',
} as const;

/**
 * API 接口路径
 */
enum Api {
  getFormFieldConfig = '/reg/formFieldConfig/list',
  saveFormFieldConfig = '/reg/formFieldConfig/save',
  updateFormFieldConfig = '/reg/formFieldConfig/update',
  replaceFormFieldConfig = '/reg/formFieldConfig/replace', // 替换配置（先删除再插入）
  deleteFormFieldConfig = '/reg/formFieldConfig',
  getActiveConfig = '/reg/formFieldConfig/active',
  getDefaultConfig = '/reg/formFieldConfig/default',
}

/**
 * 获取表单显示配置列表
 */
export const getFormDisplayConfigList = (params: { centerId?: string; formType?: string; pageNo?: number; pageSize?: number }) => {
  return defHttp.get<{
    records: FormDisplayConfig[];
    total: number;
  }>({
    url: Api.getFormFieldConfig,
    params,
  });
};

/**
 * 获取当前生效的显示配置
 */
export const getActiveFormDisplayConfig = (params: { centerId?: string; formType: string }) => {
  return defHttp.get<FormDisplayConfig>({
    url: Api.getActiveConfig,
    params: {
      centerId: params.centerId || 'default',
      formType: params.formType,
    },
  });
};

/**
 * 保存表单显示配置（新增）
 */
export const saveFormDisplayConfig = (data: FormDisplayConfig) => {
  return defHttp.post({
    url: Api.saveFormFieldConfig,
    data,
  });
};

/**
 * 更新表单显示配置（更新现有配置）
 */
export const updateFormDisplayConfig = (data: FormDisplayConfig) => {
  return defHttp.put({
    url: Api.saveFormFieldConfig,
    data,
  });
};

/**
 * 替换表单显示配置（先删除现有字段再插入新字段）
 * 这个方法专门用于解决主键冲突问题，如果后端支持的话
 */
export const replaceFormDisplayConfig = (data: FormDisplayConfig) => {
  return defHttp.post({
    url: Api.replaceFormFieldConfig,
    data,
  });
};

/**
 * 删除表单显示配置
 */
export const deleteFormDisplayConfig = (id: string) => {
  return defHttp.delete({
    url: Api.deleteFormFieldConfig + '/' + id,
  });
};

/**
 * 复制表单显示配置
 */
export const copyFormDisplayConfig = (params: {
  sourceId: string;
  configName: string;
  targetCenterId?: string;
}) => {
  return defHttp.post({
    url: Api.saveFormFieldConfig.replace('/save', '/copy'),
    params: {
      sourceId: params.sourceId,
      newConfigName: params.configName,
      targetCenterId: params.targetCenterId,
    },
  });
};

/**
 * 客户登记表单所有字段定义
 * 基于备份文件的原有逻辑，确保向后兼容
 * 根据原有的显示位置设置默认配置
 */
export const CUSTOMER_REG_CONFIGURABLE_FIELDS: FieldDisplayConfig[] = [
  // 核心字段 - 原本就在外部区域固定显示
  {
    fieldKey: 'examCategory',
    fieldName: '体检分类',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 1,
  },
  {
    fieldKey: 'appointmentDate',
    fieldName: '预约日期',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 2,
  },
  {
    fieldKey: 'name',
    fieldName: '姓名',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 3,
  },
  {
    fieldKey: 'cardType',
    fieldName: '证件类型',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 4,
  },
  {
    fieldKey: 'idCard',
    fieldName: '证件号',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 5,
  },
  {
    fieldKey: 'gender',
    fieldName: '性别',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 6,
  },
  {
    fieldKey: 'age',
    fieldName: '年龄',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 7,
  },
  {
    fieldKey: 'birthday',
    fieldName: '出生日期',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 8,
  },
  {
    fieldKey: 'phone',
    fieldName: '电话',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 9,
  },
  {
    fieldKey: 'career',
    fieldName: '职业',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '基本信息',
    sortOrder: 10,
  },

  // 条件显示字段 - 原本通过 !isFieldInCollapse() 控制，默认在外部区域
  {
    fieldKey: 'pcaCode',
    fieldName: '省市区县',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '地址信息',
    sortOrder: 11,
  },
  {
    fieldKey: 'address',
    fieldName: '详细地址',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '地址信息',
    sortOrder: 12,
  },
  {
    fieldKey: 'emergencyContact',
    fieldName: '紧急联系人',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '联系信息',
    sortOrder: 13,
  },
  {
    fieldKey: 'emergencyPhone',
    fieldName: '紧急电话',
    isVisible: true,
    displayLocation: FieldDisplayLocation.OUTSIDE,
    groupName: '联系信息',
    sortOrder: 14,
  },

  // 折叠区域字段 - 原本在折叠面板中显示的字段
  {
    fieldKey: 'nation',
    fieldName: '民族',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '详细信息',
    sortOrder: 20,
  },
  {
    fieldKey: 'bloodType',
    fieldName: '血型',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '详细信息',
    sortOrder: 21,
  },
  {
    fieldKey: 'countryCode',
    fieldName: '国籍',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '详细信息',
    sortOrder: 22,
  },
  {
    fieldKey: 'postCode',
    fieldName: '邮政编码',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '详细信息',
    sortOrder: 23,
  },
  {
    fieldKey: 'eduLevel',
    fieldName: '文化程度',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '详细信息',
    sortOrder: 24,
  },
  {
    fieldKey: 'marriageStatus',
    fieldName: '婚姻状况',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '详细信息',
    sortOrder: 25,
  },
  {
    fieldKey: 'customerCategory',
    fieldName: '客户类别',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '详细信息',
    sortOrder: 26,
  },

  // 健康信息
  {
    fieldKey: 'isPregnancyPrep',
    fieldName: '是否备孕',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '健康信息',
    sortOrder: 30,
  },

  // 证件信息
  {
    fieldKey: 'healthNo',
    fieldName: '健康证号',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '证件信息',
    sortOrder: 35,
  },
  {
    fieldKey: 'examCardNo',
    fieldName: '体检卡号',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '证件信息',
    sortOrder: 36,
  },

  // 职业信息
  {
    fieldKey: 'workYears',
    fieldName: '工龄',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '职业信息',
    sortOrder: 40,
  },

  // 单位信息
  {
    fieldKey: 'companyName',
    fieldName: '单位名称',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '单位信息',
    sortOrder: 45,
  },
  {
    fieldKey: 'belongCompany',
    fieldName: '所属单位',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '单位信息',
    sortOrder: 46,
  },
  {
    fieldKey: 'department',
    fieldName: '所属科室',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '单位信息',
    sortOrder: 47,
  },

  // 标识信息
  {
    fieldKey: 'supplyFlag',
    fieldName: '补检',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '标识信息',
    sortOrder: 50,
  },
  {
    fieldKey: 'prePayFlag',
    fieldName: '预缴',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '标识信息',
    sortOrder: 51,
  },
  {
    fieldKey: 'reExamStatus',
    fieldName: '是否复查',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '标识信息',
    sortOrder: 52,
  },
  {
    fieldKey: 'reExamRemark',
    fieldName: '复查备注',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '标识信息',
    sortOrder: 53,
  },
  // 财务信息
  {
    fieldKey: 'recipeTitle',
    fieldName: '发票抬头',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '财务信息',
    sortOrder: 60,
  },

  // 原检信息
  {
    fieldKey: 'originalIdCard',
    fieldName: '原检证件号',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '原检信息',
    sortOrder: 65,
  },
  {
    fieldKey: 'relationWithOriginal',
    fieldName: '与原检关系',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '原检信息',
    sortOrder: 66,
  },

  // 其他信息
  {
    fieldKey: 'introducer',
    fieldName: '介绍人',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '其他信息',
    sortOrder: 70,
  },
  {
    fieldKey: 'secretLevel',
    fieldName: '保密等级',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '其他信息',
    sortOrder: 71,
  },
  {
    fieldKey: 'remark',
    fieldName: '备注',
    isVisible: true,
    displayLocation: FieldDisplayLocation.COLLAPSE,
    groupName: '其他信息',
    sortOrder: 72,
  },
];

/**
 * 字段配置兼容性和迁移工具函数
 */

/**
 * 迁移旧版本字段配置到新版本
 * 为没有 displayLocation 的字段设置默认值
 * 向后兼容：没有配置的字段默认放在外部区域
 */
export function migrateFieldConfig(fields: FieldDisplayConfig[]): FieldDisplayConfig[] {
  return fields.map((field) => {
    if (!field.displayLocation) {
      // 向后兼容：没有配置的字段默认放在外部区域显示
      field.displayLocation = FieldDisplayLocation.OUTSIDE;
    }

    // 设置默认排序
    if (field.sortOrder === undefined) {
      field.sortOrder = 100; // 默认排序值
    }

    return field;
  });
}

/**
 * 验证字段配置的有效性
 */
export function validateFieldConfig(fields: FieldDisplayConfig[]): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查必需字段
  fields.forEach((field, index) => {
    if (!field.fieldKey) {
      errors.push(`字段 ${index + 1}: fieldKey 不能为空`);
    }
    if (!field.fieldName) {
      errors.push(`字段 ${field.fieldKey || index + 1}: fieldName 不能为空`);
    }
    if (!field.displayLocation) {
      warnings.push(`字段 ${field.fieldKey}: 缺少 displayLocation，将使用默认值`);
    }
  });

  // 检查重复的 fieldKey
  const fieldKeys = fields.map((f) => f.fieldKey);
  const duplicates = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);
  if (duplicates.length > 0) {
    errors.push(`发现重复的字段标识: ${duplicates.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 获取默认字段配置
 */
export function getDefaultFieldConfig(): FieldDisplayConfig[] {
  return JSON.parse(JSON.stringify(CUSTOMER_REG_CONFIGURABLE_FIELDS));
}

/**
 * 测试向后兼容性的辅助函数
 */
export function testBackwardCompatibility() {
  const defaultConfig = getDefaultFieldConfig();

  console.log('=== 向后兼容性测试 ===');
  console.log('外部区域字段（应该包含原本的核心字段）:');
  const outsideFields = defaultConfig.filter((f) => f.displayLocation === FieldDisplayLocation.OUTSIDE);
  outsideFields.forEach((f) => console.log(`- ${f.fieldName} (${f.fieldKey})`));

  console.log('\n折叠区域字段（应该包含原本的折叠面板字段）:');
  const collapseFields = defaultConfig.filter((f) => f.displayLocation === FieldDisplayLocation.COLLAPSE);
  collapseFields.forEach((f) => console.log(`- ${f.fieldName} (${f.fieldKey})`));

  console.log('\n隐藏字段:');
  const hiddenFields = defaultConfig.filter((f) => f.displayLocation === FieldDisplayLocation.HIDDEN);
  hiddenFields.forEach((f) => console.log(`- ${f.fieldName} (${f.fieldKey})`));

  console.log(`\n总计: ${defaultConfig.length} 个字段`);
  console.log(`外部区域: ${outsideFields.length} 个`);
  console.log(`折叠区域: ${collapseFields.length} 个`);
  console.log(`隐藏字段: ${hiddenFields.length} 个`);
}
