<template>
  <div class="company-auto-fill-test">
    <a-card title="所属预约自动填充单位信息测试" size="small">
      <template #extra>
        <a-space>
          <a-button @click="loadTestData" type="primary">
            <Icon icon="ant-design:reload-outlined" />
            加载测试数据
          </a-button>
          <a-button @click="clearFormData">
            <Icon icon="ant-design:clear-outlined" />
            清空表单
          </a-button>
        </a-space>
      </template>

      <!-- 测试说明 -->
      <a-alert 
        message="功能说明" 
        description="选择所属预约后，系统会自动获取预约关联的单位信息，并智能填充到表单的单位相关字段中。如果检测到单位信息不一致，会询问用户是否覆盖。"
        type="info" 
        show-icon 
        style="margin-bottom: 16px"
      />

      <!-- 表单区域 -->
      <a-row :gutter="16">
        <!-- 左侧：表单 -->
        <a-col :span="12">
          <a-card title="客户登记表单" size="small">
            <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <!-- 所属预约 -->
              <a-form-item label="所属预约">
                <a-select 
                  v-model:value="formData.companyRegId" 
                  placeholder="请选择所属预约"
                  @change="handleCompanyRegChange"
                  :options="companyRegOptions"
                />
              </a-form-item>

              <!-- 单位名称 -->
              <a-form-item label="单位名称">
                <a-input 
                  v-model:value="formData.companyName" 
                  placeholder="请输入单位名称"
                  readonly
                />
              </a-form-item>

              <!-- 单位ID -->
              <a-form-item label="单位ID">
                <a-input 
                  v-model:value="formData.companyId" 
                  placeholder="单位ID"
                  readonly
                />
              </a-form-item>

              <!-- 行业 -->
              <a-form-item label="行业">
                <a-input 
                  v-model:value="formData.industry" 
                  placeholder="请输入行业"
                  readonly
                />
              </a-form-item>

              <!-- 联系电话 -->
              <a-form-item label="联系电话">
                <a-input 
                  v-model:value="formData.phone" 
                  placeholder="请输入联系电话"
                />
              </a-form-item>

              <!-- 地址 -->
              <a-form-item label="地址">
                <a-textarea 
                  v-model:value="formData.address" 
                  placeholder="请输入地址"
                  :rows="2"
                />
              </a-form-item>

              <!-- 邮箱 -->
              <a-form-item label="邮箱">
                <a-input 
                  v-model:value="formData.email" 
                  placeholder="请输入邮箱"
                />
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>

        <!-- 右侧：日志和状态 -->
        <a-col :span="12">
          <a-card title="操作日志" size="small">
            <div class="log-container">
              <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
                <span class="log-time">{{ log.time }}</span>
                <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </a-card>

          <!-- 测试数据预览 -->
          <a-card title="测试数据预览" size="small" style="margin-top: 16px">
            <a-collapse size="small">
              <a-collapse-panel key="testData" header="查看测试数据">
                <pre>{{ JSON.stringify(testCompanyRegData, null, 2) }}</pre>
              </a-collapse-panel>
            </a-collapse>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';

// 响应式数据
const formData = reactive({
  companyRegId: null,
  companyName: '',
  companyId: '',
  industry: '',
  phone: '',
  address: '',
  email: '',
});

const operationLogs = ref([]);
const companyRegOptions = ref([]);

// 测试数据
const testCompanyRegData = {
  companyReg1: {
    id: 'reg001',
    regName: '测试预约1-科技公司体检',
    companyId: 'company001',
    company: {
      id: 'company001',
      name: '北京科技有限公司',
      workIndustry: '软件和信息技术服务业',
      telephone: '010-12345678',
      address: '北京市海淀区中关村大街1号',
      email: '<EMAIL>'
    }
  },
  companyReg2: {
    id: 'reg002',
    regName: '测试预约2-制造企业体检',
    companyId: 'company002',
    company: {
      id: 'company002',
      name: '上海制造股份有限公司',
      workIndustry: '制造业',
      telephone: '021-87654321',
      address: '上海市浦东新区张江高科技园区',
      email: '<EMAIL>'
    }
  },
  companyReg3: {
    id: 'reg003',
    regName: '测试预约3-医疗机构体检',
    companyId: 'company003',
    company: {
      id: 'company003',
      name: '广州医疗集团有限公司',
      workIndustry: '卫生和社会工作',
      telephone: '020-11223344',
      address: '广州市天河区珠江新城',
      email: '<EMAIL>'
    }
  }
};

// 方法
const addLog = (level, message) => {
  operationLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message,
  });
  
  // 限制日志数量
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50);
  }
};

const loadTestData = () => {
  // 加载测试的预约选项
  companyRegOptions.value = [
    { label: '测试预约1-科技公司体检', value: 'reg001' },
    { label: '测试预约2-制造企业体检', value: 'reg002' },
    { label: '测试预约3-医疗机构体检', value: 'reg003' },
  ];
  
  addLog('info', '测试数据已加载');
  message.success('测试数据已加载');
};

const clearFormData = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'companyRegId') {
      formData[key] = null;
    } else {
      formData[key] = '';
    }
  });
  
  addLog('info', '表单数据已清空');
  message.info('表单数据已清空');
};

// 模拟handleCompanyRegChange函数
const handleCompanyRegChange = async (companyRegId) => {
  addLog('info', `选择所属预约: ${companyRegId}`);
  
  if (!companyRegId) {
    addLog('info', '清空预约选择');
    return;
  }

  try {
    // 模拟API调用延迟
    addLog('info', '正在获取预约信息...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 获取测试数据
    const companyRegData = Object.values(testCompanyRegData).find(data => data.id === companyRegId);
    
    if (!companyRegData) {
      addLog('error', '未找到预约信息');
      return;
    }

    addLog('success', `获取预约信息成功: ${companyRegData.regName}`);
    
    // 处理单位信息自动填充
    await handleCompanyInfoAutoFill(companyRegData);
    
  } catch (error) {
    addLog('error', `获取预约信息失败: ${error.message}`);
  }
};

// 模拟单位信息自动填充逻辑
const handleCompanyInfoAutoFill = async (companyRegData) => {
  addLog('info', '开始处理单位信息自动填充');
  
  if (!companyRegData || !companyRegData.company) {
    addLog('warn', '预约中没有关联的单位信息');
    return;
  }

  const companyInfo = companyRegData.company;
  const newCompanyId = companyRegData.companyId;
  const newCompanyName = companyInfo.name;
  const newIndustry = companyInfo.workIndustry;

  addLog('info', `预约关联单位: ${newCompanyName}`);

  // 检查是否需要填充单位信息
  const needsUpdate = await checkAndConfirmCompanyUpdate(newCompanyId, newCompanyName, newIndustry);

  if (needsUpdate) {
    fillCompanyInfo(newCompanyId, newCompanyName, newIndustry, companyInfo);
  }
};

// 检查并确认是否需要更新单位信息
const checkAndConfirmCompanyUpdate = async (newCompanyId, newCompanyName, newIndustry) => {
  const currentCompanyName = formData.companyName;

  // 如果当前没有单位信息，直接填充
  if (!currentCompanyName) {
    addLog('info', '当前无单位信息，直接填充');
    return true;
  }

  // 如果单位信息一致，不需要更新
  if (currentCompanyName === newCompanyName) {
    addLog('info', '单位信息一致，无需更新');
    return false;
  }

  // 询问用户是否覆盖
  addLog('warn', `检测到单位信息不一致: 当前[${currentCompanyName}] vs 预约[${newCompanyName}]`);

  return new Promise((resolve) => {
    Modal.confirm({
      title: '发现单位信息不一致',
      content: `
        <div>
          <p>预约关联的单位信息与当前表单中的单位信息不一致：</p>
          <div style="margin: 10px 0;">
            <strong>当前单位：</strong>${currentCompanyName || '未填写'}<br/>
            <strong>预约单位：</strong>${newCompanyName || '未知'}
          </div>
          <p>是否要用预约中的单位信息覆盖当前的单位信息？</p>
        </div>
      `,
      okText: '是，覆盖',
      cancelText: '否，保持当前',
      onOk() {
        addLog('info', '用户选择覆盖单位信息');
        resolve(true);
      },
      onCancel() {
        addLog('info', '用户选择保持当前单位信息');
        resolve(false);
      },
    });
  });
};

// 执行单位信息填充
const fillCompanyInfo = (companyId, companyName, industry, companyInfo) => {
  addLog('info', '开始填充单位信息');
  
  // 填充基本单位信息
  formData.companyId = companyId;
  formData.companyName = companyName;
  formData.industry = industry;

  // 填充其他相关信息（如果表单中为空）
  if (!formData.phone && companyInfo.telephone) {
    formData.phone = companyInfo.telephone;
    addLog('info', `自动填充联系电话: ${companyInfo.telephone}`);
  }

  if (!formData.address && companyInfo.address) {
    formData.address = companyInfo.address;
    addLog('info', `自动填充地址: ${companyInfo.address}`);
  }

  if (!formData.email && companyInfo.email) {
    formData.email = companyInfo.email;
    addLog('info', `自动填充邮箱: ${companyInfo.email}`);
  }

  addLog('success', `单位信息填充完成: ${companyName}`);
  message.success(`已自动填充单位信息：${companyName}`);
};

// 组件挂载
onMounted(() => {
  loadTestData();
  addLog('info', '测试页面已加载');
});
</script>

<style scoped>
.company-auto-fill-test {
  padding: 16px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-level {
  margin-right: 8px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  min-width: 60px;
  text-align: center;
}

.log-info {
  background: #e6f7ff;
  color: #1890ff;
}

.log-success {
  background: #f6ffed;
  color: #52c41a;
}

.log-warn {
  background: #fff7e6;
  color: #fa8c16;
}

.log-error {
  background: #fff2f0;
  color: #f5222d;
}

.log-message {
  flex: 1;
}

pre {
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
