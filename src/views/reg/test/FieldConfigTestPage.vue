<template>
  <div class="field-config-test-page">
    <a-row :gutter="16">
      <!-- 左侧：调试面板 -->
      <a-col :span="8">
        <FieldConfigDebugPanel />
      </a-col>
      
      <!-- 右侧：表单预览 -->
      <a-col :span="16">
        <a-card title="表单字段显示测试" size="small">
          <template #extra>
            <a-space>
              <a-switch 
                v-model:checked="toggleSearchStatus" 
                checked-children="展开" 
                un-checked-children="折叠"
              />
              <a-button size="small" @click="testFieldDisplay">测试字段显示</a-button>
            </a-space>
          </template>
          
          <!-- 模拟表单字段显示 -->
          <div class="form-preview">
            <h4>外部区域字段（始终显示）</h4>
            <a-row :gutter="16">
              <a-col 
                v-for="field in outsideFields" 
                :key="field.fieldKey" 
                :span="12"
                class="field-item"
              >
                <div class="field-mock">
                  <label>{{ field.fieldName }}</label>
                  <a-input :placeholder="`请输入${field.fieldName}`" size="small" />
                </div>
              </a-col>
            </a-row>
            
            <a-divider />
            
            <h4>折叠区域字段（根据展开状态显示）</h4>
            <div v-if="toggleSearchStatus">
              <a-row :gutter="16">
                <a-col 
                  v-for="field in collapseFields" 
                  :key="field.fieldKey" 
                  :span="12"
                  class="field-item"
                >
                  <div class="field-mock">
                    <label>{{ field.fieldName }}</label>
                    <a-input :placeholder="`请输入${field.fieldName}`" size="small" />
                  </div>
                </a-col>
              </a-row>
            </div>
            <div v-else class="collapsed-hint">
              <a-empty description="折叠区域已收起，点击展开按钮查看更多字段" />
            </div>
            
            <a-divider />
            
            <h4>隐藏字段（不显示）</h4>
            <div class="hidden-fields-info">
              <a-tag v-for="field in hiddenFields" :key="field.fieldKey" color="red">
                {{ field.fieldName }}（已隐藏）
              </a-tag>
              <span v-if="hiddenFields.length === 0" class="no-hidden">无隐藏字段</span>
            </div>
          </div>
          
          <!-- 测试结果显示 -->
          <a-divider />
          <div class="test-results">
            <h4>字段显示测试结果</h4>
            <a-table 
              :dataSource="testResults" 
              :columns="testColumns" 
              size="small"
              :pagination="false"
            />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import FieldConfigDebugPanel from './FieldConfigDebugPanel.vue';
import { FieldDisplayLocation } from '../FormFieldConfig.api';

// 响应式数据
const toggleSearchStatus = ref(false);
const fieldDisplayConfig = ref([]);
const testResults = ref([]);

// 计算属性
const outsideFields = computed(() => {
  return fieldDisplayConfig.value.filter(f => 
    f.displayLocation === FieldDisplayLocation.OUTSIDE && f.isVisible
  );
});

const collapseFields = computed(() => {
  return fieldDisplayConfig.value.filter(f => 
    f.displayLocation === FieldDisplayLocation.COLLAPSE && f.isVisible
  );
});

const hiddenFields = computed(() => {
  return fieldDisplayConfig.value.filter(f => 
    f.displayLocation === FieldDisplayLocation.HIDDEN || !f.isVisible
  );
});

// 表格列定义
const testColumns = [
  { title: '字段名称', dataIndex: 'fieldName', width: 120 },
  { title: '配置位置', dataIndex: 'configLocation', width: 100 },
  { title: '预期显示', dataIndex: 'expectedShow', width: 80 },
  { title: '实际显示', dataIndex: 'actualShow', width: 80 },
  { 
    title: '测试结果', 
    dataIndex: 'testPassed', 
    width: 80,
    customRender: ({ text }) => text ? '✓ 通过' : '✗ 失败'
  },
];

// 方法
const loadCurrentConfig = () => {
  try {
    const localConfig = localStorage.getItem('fieldDisplayConfig');
    if (localConfig) {
      const parsedConfig = JSON.parse(localConfig);
      if (parsedConfig.fields && Array.isArray(parsedConfig.fields)) {
        fieldDisplayConfig.value = parsedConfig.fields;
        console.log('测试页面加载配置成功:', fieldDisplayConfig.value);
      }
    }
  } catch (error) {
    console.error('加载配置失败:', error);
  }
};

const shouldShowField = (fieldKey) => {
  const fieldConfig = fieldDisplayConfig.value.find(f => f.fieldKey === fieldKey);
  
  if (!fieldConfig) return true;
  if (!fieldConfig.isVisible) return false;
  
  switch (fieldConfig.displayLocation) {
    case FieldDisplayLocation.OUTSIDE:
      return true;
    case FieldDisplayLocation.COLLAPSE:
      return toggleSearchStatus.value;
    case FieldDisplayLocation.HIDDEN:
      return false;
    default:
      return true;
  }
};

const testFieldDisplay = () => {
  const results = [];
  
  fieldDisplayConfig.value.forEach(field => {
    const expectedShow = calculateExpectedShow(field);
    const actualShow = shouldShowField(field.fieldKey);
    
    results.push({
      fieldName: field.fieldName,
      configLocation: getLocationText(field.displayLocation),
      expectedShow: expectedShow ? '是' : '否',
      actualShow: actualShow ? '是' : '否',
      testPassed: expectedShow === actualShow,
    });
  });
  
  testResults.value = results;
  
  const passedCount = results.filter(r => r.testPassed).length;
  const totalCount = results.length;
  
  if (passedCount === totalCount) {
    message.success(`所有字段显示测试通过 (${passedCount}/${totalCount})`);
  } else {
    message.warning(`字段显示测试部分失败 (${passedCount}/${totalCount})`);
  }
};

const calculateExpectedShow = (field) => {
  if (!field.isVisible) return false;
  
  switch (field.displayLocation) {
    case FieldDisplayLocation.OUTSIDE:
      return true;
    case FieldDisplayLocation.COLLAPSE:
      return toggleSearchStatus.value;
    case FieldDisplayLocation.HIDDEN:
      return false;
    default:
      return true;
  }
};

const getLocationText = (location) => {
  const locationMap = {
    [FieldDisplayLocation.OUTSIDE]: '外部',
    [FieldDisplayLocation.COLLAPSE]: '折叠',
    [FieldDisplayLocation.HIDDEN]: '隐藏',
  };
  return locationMap[location] || location;
};

// 监听配置变化
watch(() => fieldDisplayConfig.value, () => {
  if (fieldDisplayConfig.value.length > 0) {
    testFieldDisplay();
  }
}, { deep: true });

// 监听外部配置变化事件
const handleFieldConfigChanged = () => {
  loadCurrentConfig();
};

// 组件挂载
onMounted(() => {
  loadCurrentConfig();
  window.addEventListener('fieldConfigChanged', handleFieldConfigChanged);
});

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('fieldConfigChanged', handleFieldConfigChanged);
});
</script>

<style scoped>
.field-config-test-page {
  padding: 16px;
}

.form-preview {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
}

.field-item {
  margin-bottom: 16px;
}

.field-mock {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.field-mock label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.collapsed-hint {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

.hidden-fields-info {
  padding: 8px;
  background: #fff2f0;
  border-radius: 4px;
  border: 1px dashed #ffccc7;
}

.no-hidden {
  color: #999;
  font-style: italic;
}

.test-results {
  margin-top: 16px;
}
</style>
