<template>
  <div class="editable-field-config-test">
    <a-card title="可编辑字段配置测试" size="small">
      <template #extra>
        <a-space>
          <a-button @click="openConfigModal" type="primary">
            <Icon icon="ant-design:setting-outlined" />
            打开字段配置
          </a-button>
          <a-button @click="loadTestData">
            <Icon icon="ant-design:reload-outlined" />
            加载测试数据
          </a-button>
        </a-space>
      </template>

      <!-- 当前配置预览 -->
      <div class="config-preview">
        <h4>当前字段配置预览</h4>
        <a-table 
          :dataSource="currentFields" 
          :columns="previewColumns" 
          size="small"
          :pagination="false"
          :scroll="{ y: 400 }"
        />
      </div>

      <!-- 字段配置弹窗 -->
      <FormFieldConfigModal
        ref="configModalRef"
        @success="handleConfigSuccess"
      />
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import FormFieldConfigModal from '../components/FormFieldConfigModal.vue';
import { FieldDisplayLocation } from '../FormFieldConfig.api';

// 响应式数据
const configModalRef = ref();
const currentFields = ref([]);

// 预览表格列定义
const previewColumns = [
  { title: '字段标识', dataIndex: 'fieldKey', width: 120 },
  { title: '字段名称', dataIndex: 'fieldName', width: 120 },
  { title: '所属分类', dataIndex: 'groupName', width: 120 },
  { 
    title: '显示位置', 
    dataIndex: 'displayLocation', 
    width: 100,
    customRender: ({ text }) => {
      const locationMap = {
        [FieldDisplayLocation.OUTSIDE]: '外部',
        [FieldDisplayLocation.COLLAPSE]: '折叠',
        [FieldDisplayLocation.HIDDEN]: '隐藏',
      };
      return locationMap[text] || text;
    }
  },
  { 
    title: '是否可见', 
    dataIndex: 'isVisible', 
    width: 80,
    customRender: ({ text }) => text ? '是' : '否'
  },
  { title: '排序', dataIndex: 'sortOrder', width: 60 },
];

// 测试数据
const testFieldData = [
  {
    fieldKey: 'name',
    fieldName: '姓名',
    groupName: '基本信息',
    displayLocation: FieldDisplayLocation.OUTSIDE,
    isVisible: true,
    sortOrder: 1,
  },
  {
    fieldKey: 'idCard',
    fieldName: '身份证号',
    groupName: '证件信息',
    displayLocation: FieldDisplayLocation.OUTSIDE,
    isVisible: true,
    sortOrder: 2,
  },
  {
    fieldKey: 'phone',
    fieldName: '手机号码',
    groupName: '联系信息',
    displayLocation: FieldDisplayLocation.OUTSIDE,
    isVisible: true,
    sortOrder: 3,
  },
  {
    fieldKey: 'email',
    fieldName: '电子邮箱',
    groupName: '联系信息',
    displayLocation: FieldDisplayLocation.COLLAPSE,
    isVisible: true,
    sortOrder: 4,
  },
  {
    fieldKey: 'address',
    fieldName: '详细地址',
    groupName: '地址信息',
    displayLocation: FieldDisplayLocation.COLLAPSE,
    isVisible: true,
    sortOrder: 5,
  },
  {
    fieldKey: 'nation',
    fieldName: '民族',
    groupName: '详细信息',
    displayLocation: FieldDisplayLocation.HIDDEN,
    isVisible: false,
    sortOrder: 6,
  },
  {
    fieldKey: 'bloodType',
    fieldName: '血型',
    groupName: '健康信息',
    displayLocation: FieldDisplayLocation.COLLAPSE,
    isVisible: true,
    sortOrder: 7,
  },
  {
    fieldKey: 'company',
    fieldName: '工作单位',
    groupName: '单位信息',
    displayLocation: FieldDisplayLocation.OUTSIDE,
    isVisible: true,
    sortOrder: 8,
  },
];

// 方法
const loadCurrentConfig = () => {
  try {
    const localConfig = localStorage.getItem('fieldDisplayConfig');
    if (localConfig) {
      const parsedConfig = JSON.parse(localConfig);
      if (parsedConfig.fields && Array.isArray(parsedConfig.fields)) {
        currentFields.value = parsedConfig.fields;
        console.log('加载当前配置成功:', currentFields.value);
      }
    } else {
      // 如果没有配置，使用测试数据
      currentFields.value = [...testFieldData];
    }
  } catch (error) {
    console.error('加载配置失败:', error);
    currentFields.value = [...testFieldData];
  }
};

const loadTestData = () => {
  // 保存测试数据到localStorage
  localStorage.setItem('fieldDisplayConfig', JSON.stringify({
    fields: testFieldData,
  }));
  
  currentFields.value = [...testFieldData];
  message.success('测试数据已加载');
};

const openConfigModal = () => {
  if (configModalRef.value) {
    // 传递当前配置到弹窗
    const configData = {
      configName: '测试配置',
      centerId: 'test_center',
      centerName: '测试体检中心',
      formType: 'customer_reg',
      isActive: true,
      fields: [...currentFields.value],
    };
    
    configModalRef.value.openModal(configData);
  }
};

const handleConfigSuccess = () => {
  console.log('配置保存成功，重新加载配置');
  loadCurrentConfig();
  message.success('字段配置已更新');
};

// 组件挂载
onMounted(() => {
  loadCurrentConfig();
});
</script>

<style scoped>
.editable-field-config-test {
  padding: 16px;
}

.config-preview {
  margin-top: 16px;
}

.config-preview h4 {
  margin-bottom: 12px;
  color: #333;
  font-weight: 500;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 500;
}
</style>
