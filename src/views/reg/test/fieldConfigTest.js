/**
 * 字段配置功能测试文件
 * 用于验证字段配置的加载、显示和应用逻辑
 */

import { FieldDisplayLocation } from '../FormFieldConfig.api';

/**
 * 测试用的字段配置数据
 */
export const testFieldConfigs = {
  // 测试配置1：部分字段在外部显示
  config1: {
    configName: '测试配置1-外部显示',
    centerId: 'test_center_1',
    centerName: '测试体检中心1',
    formType: 'customer_reg',
    isActive: true,
    fields: [
      {
        fieldKey: 'name',
        fieldName: '姓名',
        isVisible: true,
        displayLocation: FieldDisplayLocation.OUTSIDE,
        groupName: '基本信息',
        sortOrder: 1,
      },
      {
        fieldKey: 'idCard',
        fieldName: '证件号',
        isVisible: true,
        displayLocation: FieldDisplayLocation.OUTSIDE,
        groupName: '基本信息',
        sortOrder: 2,
      },
      {
        fieldKey: 'phone',
        fieldName: '电话',
        isVisible: true,
        displayLocation: FieldDisplayLocation.OUTSIDE,
        groupName: '基本信息',
        sortOrder: 3,
      },
      {
        fieldKey: 'nation',
        fieldName: '民族',
        isVisible: true,
        displayLocation: FieldDisplayLocation.COLLAPSE,
        groupName: '详细信息',
        sortOrder: 10,
      },
      {
        fieldKey: 'bloodType',
        fieldName: '血型',
        isVisible: true,
        displayLocation: FieldDisplayLocation.HIDDEN,
        groupName: '详细信息',
        sortOrder: 11,
      },
    ],
  },

  // 测试配置2：更多字段在外部显示
  config2: {
    configName: '测试配置2-更多外部显示',
    centerId: 'test_center_2',
    centerName: '测试体检中心2',
    formType: 'customer_reg',
    isActive: true,
    fields: [
      {
        fieldKey: 'name',
        fieldName: '姓名',
        isVisible: true,
        displayLocation: FieldDisplayLocation.OUTSIDE,
        groupName: '基本信息',
        sortOrder: 1,
      },
      {
        fieldKey: 'idCard',
        fieldName: '证件号',
        isVisible: true,
        displayLocation: FieldDisplayLocation.OUTSIDE,
        groupName: '基本信息',
        sortOrder: 2,
      },
      {
        fieldKey: 'phone',
        fieldName: '电话',
        isVisible: true,
        displayLocation: FieldDisplayLocation.OUTSIDE,
        groupName: '基本信息',
        sortOrder: 3,
      },
      {
        fieldKey: 'nation',
        fieldName: '民族',
        isVisible: true,
        displayLocation: FieldDisplayLocation.OUTSIDE,
        groupName: '基本信息',
        sortOrder: 4,
      },
      {
        fieldKey: 'bloodType',
        fieldName: '血型',
        isVisible: true,
        displayLocation: FieldDisplayLocation.OUTSIDE,
        groupName: '基本信息',
        sortOrder: 5,
      },
      {
        fieldKey: 'marriageStatus',
        fieldName: '婚姻状况',
        isVisible: true,
        displayLocation: FieldDisplayLocation.COLLAPSE,
        groupName: '详细信息',
        sortOrder: 10,
      },
    ],
  },

  // 测试配置3：所有字段都在折叠区域
  config3: {
    configName: '测试配置3-全部折叠',
    centerId: 'test_center_3',
    centerName: '测试体检中心3',
    formType: 'customer_reg',
    isActive: true,
    fields: [
      {
        fieldKey: 'name',
        fieldName: '姓名',
        isVisible: true,
        displayLocation: FieldDisplayLocation.COLLAPSE,
        groupName: '基本信息',
        sortOrder: 1,
      },
      {
        fieldKey: 'idCard',
        fieldName: '证件号',
        isVisible: true,
        displayLocation: FieldDisplayLocation.COLLAPSE,
        groupName: '基本信息',
        sortOrder: 2,
      },
      {
        fieldKey: 'phone',
        fieldName: '电话',
        isVisible: true,
        displayLocation: FieldDisplayLocation.COLLAPSE,
        groupName: '基本信息',
        sortOrder: 3,
      },
      {
        fieldKey: 'nation',
        fieldName: '民族',
        isVisible: true,
        displayLocation: FieldDisplayLocation.COLLAPSE,
        groupName: '详细信息',
        sortOrder: 10,
      },
    ],
  },
};

/**
 * 测试字段显示逻辑的辅助函数
 */
export const testFieldDisplayLogic = {
  /**
   * 测试shouldShowField函数
   */
  testShouldShowField(shouldShowFieldFn, fieldConfig, toggleStatus = false) {
    const results = [];
    
    fieldConfig.fields.forEach(field => {
      const shouldShow = shouldShowFieldFn(field.fieldKey);
      results.push({
        fieldKey: field.fieldKey,
        fieldName: field.fieldName,
        displayLocation: field.displayLocation,
        expectedShow: this.calculateExpectedShow(field, toggleStatus),
        actualShow: shouldShow,
        passed: shouldShow === this.calculateExpectedShow(field, toggleStatus),
      });
    });
    
    return results;
  },

  /**
   * 计算字段预期的显示状态
   */
  calculateExpectedShow(field, toggleStatus) {
    if (!field.isVisible) return false;
    
    switch (field.displayLocation) {
      case FieldDisplayLocation.OUTSIDE:
        return true;
      case FieldDisplayLocation.COLLAPSE:
        return toggleStatus;
      case FieldDisplayLocation.HIDDEN:
        return false;
      default:
        return true;
    }
  },

  /**
   * 运行完整的测试套件
   */
  runFullTest(shouldShowFieldFn, fieldConfig) {
    console.log(`=== 开始测试配置: ${fieldConfig.configName} ===`);
    
    // 测试折叠状态为false时的显示
    console.log('测试折叠状态为false时的字段显示:');
    const resultsCollapsed = this.testShouldShowField(shouldShowFieldFn, fieldConfig, false);
    resultsCollapsed.forEach(result => {
      console.log(`${result.fieldName}: 预期=${result.expectedShow}, 实际=${result.actualShow}, ${result.passed ? '✓' : '✗'}`);
    });
    
    // 测试折叠状态为true时的显示
    console.log('测试折叠状态为true时的字段显示:');
    const resultsExpanded = this.testShouldShowField(shouldShowFieldFn, fieldConfig, true);
    resultsExpanded.forEach(result => {
      console.log(`${result.fieldName}: 预期=${result.expectedShow}, 实际=${result.actualShow}, ${result.passed ? '✓' : '✗'}`);
    });
    
    const allPassed = [...resultsCollapsed, ...resultsExpanded].every(r => r.passed);
    console.log(`测试结果: ${allPassed ? '全部通过 ✓' : '存在失败 ✗'}`);
    
    return allPassed;
  },
};

/**
 * 模拟本地存储的测试数据
 */
export const mockLocalStorageData = {
  validConfig: JSON.stringify({
    fields: testFieldConfigs.config1.fields,
  }),
  
  invalidConfig: JSON.stringify({
    fields: 'invalid_data',
  }),
  
  emptyConfig: JSON.stringify({
    fields: [],
  }),
};

/**
 * 控制台测试函数
 */
export const runConsoleTests = () => {
  console.log('=== 字段配置功能测试开始 ===');
  
  // 测试配置数据的有效性
  Object.keys(testFieldConfigs).forEach(configKey => {
    const config = testFieldConfigs[configKey];
    console.log(`配置 ${configKey}:`, config);
    console.log(`字段数量: ${config.fields.length}`);
    console.log(`外部字段: ${config.fields.filter(f => f.displayLocation === FieldDisplayLocation.OUTSIDE).length}`);
    console.log(`折叠字段: ${config.fields.filter(f => f.displayLocation === FieldDisplayLocation.COLLAPSE).length}`);
    console.log(`隐藏字段: ${config.fields.filter(f => f.displayLocation === FieldDisplayLocation.HIDDEN).length}`);
  });
  
  console.log('=== 字段配置功能测试完成 ===');
};
