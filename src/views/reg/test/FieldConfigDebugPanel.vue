<template>
  <div class="field-config-debug-panel">
    <a-card title="字段配置调试面板" size="small">
      <template #extra>
        <a-space>
          <a-button size="small" @click="refreshConfig">刷新配置</a-button>
          <a-button size="small" @click="clearCache">清除缓存</a-button>
          <a-button size="small" @click="runTests">运行测试</a-button>
        </a-space>
      </template>

      <!-- 配置状态信息 -->
      <a-row :gutter="16" class="debug-section">
        <a-col :span="8">
          <a-statistic title="配置字段总数" :value="configStats.total" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="外部显示字段" :value="configStats.outside" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="折叠区域字段" :value="configStats.collapse" />
        </a-col>
      </a-row>

      <a-divider />

      <!-- 配置详情 -->
      <a-collapse v-model:activeKey="activeKeys" size="small">
        <a-collapse-panel key="config" header="当前配置详情">
          <a-table 
            :dataSource="configTableData" 
            :columns="configColumns" 
            size="small"
            :pagination="false"
            :scroll="{ y: 300 }"
          />
        </a-collapse-panel>

        <a-collapse-panel key="test" header="测试配置">
          <a-space direction="vertical" style="width: 100%">
            <a-select 
              v-model:value="selectedTestConfig" 
              placeholder="选择测试配置"
              style="width: 200px"
            >
              <a-select-option value="config1">测试配置1-外部显示</a-select-option>
              <a-select-option value="config2">测试配置2-更多外部显示</a-select-option>
              <a-select-option value="config3">测试配置3-全部折叠</a-select-option>
            </a-select>
            <a-button @click="applyTestConfig" type="primary" size="small">
              应用测试配置
            </a-button>
          </a-space>
        </a-collapse-panel>

        <a-collapse-panel key="logs" header="调试日志">
          <div class="debug-logs">
            <div v-for="(log, index) in debugLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { FieldDisplayLocation } from '../FormFieldConfig.api';
import { testFieldConfigs } from './fieldConfigTest.js';

// 响应式数据
const activeKeys = ref(['config']);
const selectedTestConfig = ref('config1');
const debugLogs = ref([]);
const currentConfig = ref([]);

// 配置统计
const configStats = computed(() => {
  const total = currentConfig.value.length;
  const outside = currentConfig.value.filter(f => f.displayLocation === FieldDisplayLocation.OUTSIDE).length;
  const collapse = currentConfig.value.filter(f => f.displayLocation === FieldDisplayLocation.COLLAPSE).length;
  const hidden = currentConfig.value.filter(f => f.displayLocation === FieldDisplayLocation.HIDDEN).length;
  
  return { total, outside, collapse, hidden };
});

// 表格数据
const configTableData = computed(() => {
  return currentConfig.value.map((field, index) => ({
    key: index,
    fieldKey: field.fieldKey,
    fieldName: field.fieldName,
    displayLocation: field.displayLocation,
    isVisible: field.isVisible,
    groupName: field.groupName,
    sortOrder: field.sortOrder,
  }));
});

// 表格列定义
const configColumns = [
  { title: '字段标识', dataIndex: 'fieldKey', width: 120 },
  { title: '字段名称', dataIndex: 'fieldName', width: 100 },
  { 
    title: '显示位置', 
    dataIndex: 'displayLocation', 
    width: 100,
    customRender: ({ text }) => {
      const locationMap = {
        [FieldDisplayLocation.OUTSIDE]: '外部',
        [FieldDisplayLocation.COLLAPSE]: '折叠',
        [FieldDisplayLocation.HIDDEN]: '隐藏',
      };
      return locationMap[text] || text;
    }
  },
  { 
    title: '是否可见', 
    dataIndex: 'isVisible', 
    width: 80,
    customRender: ({ text }) => text ? '是' : '否'
  },
  { title: '分组', dataIndex: 'groupName', width: 100 },
  { title: '排序', dataIndex: 'sortOrder', width: 60 },
];

// 方法
const addLog = (level, message) => {
  debugLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message,
  });
  
  // 限制日志数量
  if (debugLogs.value.length > 100) {
    debugLogs.value = debugLogs.value.slice(0, 100);
  }
};

const refreshConfig = () => {
  try {
    // 从localStorage读取当前配置
    const localConfig = localStorage.getItem('fieldDisplayConfig');
    if (localConfig) {
      const parsedConfig = JSON.parse(localConfig);
      if (parsedConfig.fields && Array.isArray(parsedConfig.fields)) {
        currentConfig.value = parsedConfig.fields;
        addLog('info', `配置刷新成功，加载了 ${parsedConfig.fields.length} 个字段配置`);
      } else {
        addLog('warn', '本地配置格式不正确');
      }
    } else {
      addLog('warn', '未找到本地配置');
    }
  } catch (error) {
    addLog('error', `刷新配置失败: ${error.message}`);
  }
};

const clearCache = () => {
  try {
    localStorage.removeItem('fieldDisplayConfig');
    sessionStorage.removeItem('fieldDisplayConfig_customer_reg_default_center');
    addLog('info', '缓存清除成功');
    message.success('缓存已清除');
  } catch (error) {
    addLog('error', `清除缓存失败: ${error.message}`);
  }
};

const applyTestConfig = () => {
  try {
    const testConfig = testFieldConfigs[selectedTestConfig.value];
    if (testConfig) {
      // 保存测试配置到localStorage
      localStorage.setItem('fieldDisplayConfig', JSON.stringify({
        fields: testConfig.fields,
      }));
      
      // 更新当前显示的配置
      currentConfig.value = testConfig.fields;
      
      addLog('info', `应用测试配置成功: ${testConfig.configName}`);
      message.success(`已应用测试配置: ${testConfig.configName}`);
      
      // 触发页面刷新配置
      window.dispatchEvent(new CustomEvent('fieldConfigChanged'));
    }
  } catch (error) {
    addLog('error', `应用测试配置失败: ${error.message}`);
  }
};

const runTests = () => {
  addLog('info', '开始运行字段配置测试...');
  
  // 这里可以添加更多的测试逻辑
  try {
    // 测试配置的有效性
    const config = currentConfig.value;
    const hasOutsideFields = config.some(f => f.displayLocation === FieldDisplayLocation.OUTSIDE);
    const hasCollapseFields = config.some(f => f.displayLocation === FieldDisplayLocation.COLLAPSE);
    
    addLog('info', `测试结果: 外部字段=${hasOutsideFields ? '有' : '无'}, 折叠字段=${hasCollapseFields ? '有' : '无'}`);
    
    // 验证字段配置的完整性
    const requiredFields = ['fieldKey', 'fieldName', 'displayLocation', 'isVisible'];
    const invalidFields = config.filter(field => 
      !requiredFields.every(prop => field.hasOwnProperty(prop))
    );
    
    if (invalidFields.length > 0) {
      addLog('warn', `发现 ${invalidFields.length} 个配置不完整的字段`);
    } else {
      addLog('info', '所有字段配置完整性检查通过');
    }
    
    message.success('测试完成，请查看调试日志');
  } catch (error) {
    addLog('error', `测试执行失败: ${error.message}`);
  }
};

// 组件挂载时初始化
onMounted(() => {
  refreshConfig();
  addLog('info', '字段配置调试面板已加载');
});
</script>

<style scoped>
.field-config-debug-panel {
  margin: 16px;
}

.debug-section {
  margin-bottom: 16px;
}

.debug-logs {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-level {
  margin-right: 8px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  min-width: 50px;
  text-align: center;
}

.log-info {
  background: #e6f7ff;
  color: #1890ff;
}

.log-warn {
  background: #fff7e6;
  color: #fa8c16;
}

.log-error {
  background: #fff2f0;
  color: #f5222d;
}

.log-message {
  flex: 1;
}
</style>
