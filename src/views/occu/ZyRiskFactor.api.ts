import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/occu/zyRiskFactor/list',
  save='/occu/zyRiskFactor/add',
  edit='/occu/zyRiskFactor/edit',
  deleteOne = '/occu/zyRiskFactor/delete',
  deleteBatch = '/occu/zyRiskFactor/deleteBatch',
  importExcel = '/occu/zyRiskFactor/importExcel',
  exportXls = '/occu/zyRiskFactor/exportXls',
  zyRiskFactorItemgroupList = '/occu/zyRiskFactor/queryZyRiskFactorItemgroupByMainId',
  zyRiskFactorWorktypeList = '/occu/zyRiskFactor/queryZyRiskFactorWorktypeByMainId',
  test = '/occu/zyRiskFactor/test',
  batchUpdateHelpCharAndWubiCode = '/occu/zyRiskFactor/batchUpdateHelpCharAndWubiCode',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 子表单查询接口
 * @param params
 */
export const queryZyRiskFactorItemgroup = Api.zyRiskFactorItemgroupList
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
/**
 * 子表列表接口
 * @param params
 */
export const zyRiskFactorItemgroupList = (params) =>
  defHttp.get({url: Api.zyRiskFactorItemgroupList, params},{isTransformResponse:false});

/**
 * 工种关联列表接口
 * @param params
 */
export const zyRiskFactorWorktypeList = (params) =>
  defHttp.get({url: Api.zyRiskFactorWorktypeList, params},{isTransformResponse:false});

/**
 * 测试接口连通性
 */
export const testConnection = () => {
  return defHttp.get({url: Api.test});
};

/**
 * 一键补充助记码和五笔简码
 */
export const batchUpdateHelpCharAndWubiCode = () => {
  return defHttp.post({url: Api.batchUpdateHelpCharAndWubiCode});
};
