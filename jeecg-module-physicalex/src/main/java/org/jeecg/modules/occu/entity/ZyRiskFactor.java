package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 危害因素
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@ApiModel(value="zy_risk_factor对象", description="危害因素")
@Data
@TableName("zy_risk_factor")
public class ZyRiskFactor implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private java.lang.Integer sort;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
	/**代码*/
	@Excel(name = "代码", width = 15)
    @ApiModelProperty(value = "代码")
    private java.lang.String code;
	/**类别*/
	@Excel(name = "类别", width = 15, dictTable = "zy_risk_factor_type where enable_flag=1", dicText = "name", dicCode = "id")
    @Dict(dictTable = "zy_risk_factor_type where enable_flag=1", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "类别")
    private java.lang.String typeId;
	/**介绍说明*/
	@Excel(name = "介绍说明", width = 15)
    @ApiModelProperty(value = "介绍说明")
    private java.lang.String remark;
	/**启用*/
    @Excel(name = "启用", width = 15,replace = {"是_1","否_0"} )
    @ApiModelProperty(value = "启用")
    private java.lang.Integer valid;
	/**系统内置*/
    @Excel(name = "系统内置", width = 15,replace = {"是_1","否_0"} )
    @ApiModelProperty(value = "系统内置")
    private java.lang.String sysFlag;
	/**关联工种*/
	@Excel(name = "关联工种", width = 15, dictTable = "zy_worktype where enable_flag=1", dicText = "name", dicCode = "id")
    @Dict(dictTable = "zy_worktype where enable_flag=1", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "关联工种")
    private java.lang.String zyWorkType;
	/**防护说明*/
	@Excel(name = "防护说明", width = 15)
    @ApiModelProperty(value = "防护说明")
    private java.lang.String protectContent;
	/**防护措施*/
	@Excel(name = "防护措施", width = 15)
    @ApiModelProperty(value = "防护措施")
    private java.lang.String protectAdvice;
	/**岗位类别*/
	@Excel(name = "岗位类别", width = 15)
    @ApiModelProperty(value = "岗位类别")
    private java.lang.String postStateId;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**助记码*/
    @Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private String helpChar;
    /**五笔简码*/
    @Excel(name = "五笔简码", width = 15)
    @ApiModelProperty(value = "五笔简码")
    private String wubiCode;
}
