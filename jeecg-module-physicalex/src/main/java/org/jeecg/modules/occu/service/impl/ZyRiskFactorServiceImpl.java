package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.basicinfo.entity.CheckPartDict;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.mapper.CheckPartDictMapper;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.mapper.ZyRiskFactorItemgroupMapper;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.occu.mapper.ZyRiskFactorWorktypeMapper;
import org.jeecg.modules.occu.mapper.ZyWorktypeMapper;
import org.jeecg.modules.occu.service.IZyRiskFactorService;
import org.jeecg.modules.basicinfo.util.PinyinUtil;
import org.jeecg.modules.basicinfo.util.WubiUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 危害因素 Service
 */
@Service
public class ZyRiskFactorServiceImpl extends ServiceImpl<ZyRiskFactorMapper, ZyRiskFactor> implements IZyRiskFactorService {

    @Autowired
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Autowired
    private ZyRiskFactorItemgroupMapper zyRiskFactorItemgroupMapper;
    @Autowired
    private ZyRiskFactorWorktypeMapper zyRiskFactorWorktypeMapper;
    @Autowired
    private ZyWorktypeMapper zyWorktypeMapper;
    @Autowired
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    private CheckPartDictMapper checkPartDictMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(zyRiskFactor);
        zyRiskFactorMapper.insert(zyRiskFactor);
        if (zyRiskFactorItemgroupList != null && !zyRiskFactorItemgroupList.isEmpty()) {
            for (ZyRiskFactorItemgroup entity : zyRiskFactorItemgroupList) {
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                // 补充相关信息
                enrichItemgroupInfo(entity, zyRiskFactor.getName());
                zyRiskFactorItemgroupMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList, List<ZyRiskFactorWorktype> zyRiskFactorWorktypeList) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(zyRiskFactor);
        zyRiskFactorMapper.insert(zyRiskFactor);
        // 必检项目
        if (zyRiskFactorItemgroupList != null && !zyRiskFactorItemgroupList.isEmpty()) {
            for (ZyRiskFactorItemgroup entity : zyRiskFactorItemgroupList) {
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                // 补充相关信息
                enrichItemgroupInfo(entity, zyRiskFactor.getName());
                zyRiskFactorItemgroupMapper.insert(entity);
            }
        }
        // 工种关联
        if (zyRiskFactorWorktypeList != null && !zyRiskFactorWorktypeList.isEmpty()) {
            for (ZyRiskFactorWorktype entity : zyRiskFactorWorktypeList) {
                ZyWorktype worktype = zyWorktypeMapper.selectById(entity.getWorktypeId());
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());

                entity.setWorktypeId(worktype.getId());
                entity.setWorktypeName(worktype.getName());
                entity.setWorktypeCode(worktype.getCode());
                zyRiskFactorWorktypeMapper.insert(entity);
            }
        }
    }

    private Map<String, String> buildWorktypeCodeMap(List<ZyRiskFactorWorktype> list) {
        Set<String> ids = list.stream().map(ZyRiskFactorWorktype::getWorktypeId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (ids.isEmpty()) return new HashMap<>();
        return zyWorktypeMapper.selectBatchIds(ids).stream().filter(w -> w.getId() != null).collect(Collectors.toMap(w -> w.getId(), w -> w.getCode(), (a, b) -> a));
    }

    /**
     * 补充必检项目的相关信息
     * @param entity 必检项目实体
     * @param factorName 危害因素名称
     */
    private void enrichItemgroupInfo(ZyRiskFactorItemgroup entity, String factorName) {
        // 设置危害因素名称
        entity.setFactorName(factorName);

        // 查询并设置项目组合信息
        if (StringUtils.isNotBlank(entity.getItemgroupId())) {
            ItemGroup itemGroup = itemGroupMapper.selectById(entity.getItemgroupId());
            if (itemGroup != null) {
                entity.setGroupName(StringUtils.isNotBlank(itemGroup.getHisName()) ? itemGroup.getHisName() : itemGroup.getName());
                entity.setGroupCode(itemGroup.getHisCode());
            }
        }

        // 查询并设置检查部位信息
        if (StringUtils.isNotBlank(entity.getCheckPartCode())) {
            LambdaQueryWrapper<CheckPartDict> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CheckPartDict::getCode, entity.getCheckPartCode());
            CheckPartDict checkPart = checkPartDictMapper.selectOne(wrapper);
            if (checkPart != null) {
                entity.setCheckPartId(checkPart.getId());
                entity.setCheckPartName(checkPart.getName());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(zyRiskFactor);
        zyRiskFactorMapper.updateById(zyRiskFactor);
        zyRiskFactorItemgroupMapper.deleteByMainId(zyRiskFactor.getId());
        if (zyRiskFactorItemgroupList != null && !zyRiskFactorItemgroupList.isEmpty()) {
            for (ZyRiskFactorItemgroup entity : zyRiskFactorItemgroupList) {
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                // 补充相关信息
                enrichItemgroupInfo(entity, zyRiskFactor.getName());
                zyRiskFactorItemgroupMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList, List<ZyRiskFactorWorktype> zyRiskFactorWorktypeList) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(zyRiskFactor);
        zyRiskFactorMapper.updateById(zyRiskFactor);
        // 删除旧子表
        zyRiskFactorItemgroupMapper.deleteByMainId(zyRiskFactor.getId());
        zyRiskFactorWorktypeMapper.deleteByMainId(zyRiskFactor.getId());
        // 重新插入必检项目
        if (zyRiskFactorItemgroupList != null && !zyRiskFactorItemgroupList.isEmpty()) {
            for (ZyRiskFactorItemgroup entity : zyRiskFactorItemgroupList) {
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                // 补充相关信息
                enrichItemgroupInfo(entity, zyRiskFactor.getName());
                zyRiskFactorItemgroupMapper.insert(entity);
            }
        }
        // 重新插入工种关联并补充代码
        if (zyRiskFactorWorktypeList != null && !zyRiskFactorWorktypeList.isEmpty()) {
            Map<String, String> worktypeCodeMap = buildWorktypeCodeMap(zyRiskFactorWorktypeList);
            for (ZyRiskFactorWorktype entity : zyRiskFactorWorktypeList) {
                ZyWorktype worktype = zyWorktypeMapper.selectById(entity.getWorktypeId());
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                entity.setWorktypeCode(worktype.getCode());
                entity.setWorktypeName(worktype.getName());
                entity.setWorktypeId(worktype.getId());
                zyRiskFactorWorktypeMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        zyRiskFactorItemgroupMapper.deleteByMainId(id);
        zyRiskFactorWorktypeMapper.deleteByMainId(id);
        zyRiskFactorMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            zyRiskFactorItemgroupMapper.deleteByMainId(id.toString());
            zyRiskFactorWorktypeMapper.deleteByMainId(id.toString());
            zyRiskFactorMapper.deleteById(id);
        }
    }

    @Override
    public List<ZyRiskFactor> getRiskFactorsByWorktype(String worktypeId) {
        if (StringUtils.isBlank(worktypeId)) {
            return new ArrayList<>();
        }
        List<ZyRiskFactorWorktype> worktypeList = zyRiskFactorWorktypeMapper.selectList(new LambdaQueryWrapper<ZyRiskFactorWorktype>().eq(ZyRiskFactorWorktype::getWorktypeId, worktypeId).orderByAsc(ZyRiskFactorWorktype::getSeq));
        if (worktypeList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> factorIds = worktypeList.stream().map(ZyRiskFactorWorktype::getFactorId).collect(Collectors.toList());
        return zyRiskFactorMapper.selectBatchIds(factorIds);
    }



    @Override
    public List<ZyRiskFactor> getRiskFactorsByWorktypeCode(String workTypeCode) {
        if (StringUtils.isBlank(workTypeCode)) {
            return new ArrayList<>();
        }
        List<ZyRiskFactorWorktype> worktypeList = zyRiskFactorWorktypeMapper.selectList(new LambdaQueryWrapper<ZyRiskFactorWorktype>().eq(ZyRiskFactorWorktype::getWorktypeCode, workTypeCode).orderByAsc(ZyRiskFactorWorktype::getSeq));
        if (worktypeList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> factorIds = worktypeList.stream().map(ZyRiskFactorWorktype::getFactorId).collect(Collectors.toList());
        return zyRiskFactorMapper.selectBatchIds(factorIds);
    }

    /**
     * 自动生成助记码和五笔简码
     * @param zyRiskFactor 危害因素实体
     */
    private void generateHelpCharAndWubiCode(ZyRiskFactor zyRiskFactor) {
        if (StringUtils.isNotBlank(zyRiskFactor.getName())) {
            // 如果助记码为空，自动生成拼音首字母
            if (StringUtils.isBlank(zyRiskFactor.getHelpChar())) {
                zyRiskFactor.setHelpChar(PinyinUtil.generateSmartHelpChar(zyRiskFactor.getName()));
            }
            // 如果五笔简码为空，自动生成五笔简码
            if (StringUtils.isBlank(zyRiskFactor.getWubiCode())) {
                zyRiskFactor.setWubiCode(WubiUtil.generateSmartWubiCode(zyRiskFactor.getName()));
            }
        }
    }

}
