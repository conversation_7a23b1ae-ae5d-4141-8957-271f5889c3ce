package org.jeecg.modules.basicinfo.util;

/**
 * @Description: 工具类测试主方法
 * @Author: jeecg-boot
 * @Date: 2025-01-27
 * @Version: V1.0
 */
public class UtilTestMain {

    public static void main(String[] args) {
        System.out.println("=== 测试拼音工具类 ===");
        
        // 测试数据
        String[] testStrings = {
            "电焊工",
            "机械操作员",
            "危害因素",
            "噪声粉尘",
            "高温低温",
            "化学物质",
            "Test123",
            "测试ABC"
        };
        
        for (String testStr : testStrings) {
            try {
                String firstChars = PinyinUtil.getFirstChars(testStr);
                String fullPinyin = PinyinUtil.getFullPinyin(testStr);
                String smartHelpChar = PinyinUtil.generateSmartHelpChar(testStr);
                boolean containsChinese = PinyinUtil.containsChinese(testStr);
                
                System.out.println("原文: '" + testStr + "' -> 首字母: '" + firstChars + 
                        "', 全拼: '" + fullPinyin + "', 智能助记码: '" + smartHelpChar + 
                        "', 包含中文: " + containsChinese);
            } catch (Exception e) {
                System.err.println("拼音处理失败: " + testStr + " - " + e.getMessage());
            }
        }
        
        System.out.println("\n=== 测试五笔工具类 ===");
        
        for (String testStr : testStrings) {
            try {
                String wubiCode = WubiUtil.getWubiCode(testStr);
                String fullWubiCode = WubiUtil.getFullWubiCode(testStr);
                String smartWubiCode = WubiUtil.generateSmartWubiCode(testStr);
                boolean containsChinese = WubiUtil.containsChinese(testStr);
                
                System.out.println("原文: '" + testStr + "' -> 五笔简码: '" + wubiCode + 
                        "', 完整五笔: '" + fullWubiCode + "', 智能五笔: '" + smartWubiCode + 
                        "', 包含中文: " + containsChinese);
            } catch (Exception e) {
                System.err.println("五笔处理失败: " + testStr + " - " + e.getMessage());
            }
        }
        
        System.out.println("\n=== 测试常见危害因素编码 ===");
        
        // 常见危害因素
        String[] riskFactors = {
            "噪声",
            "粉尘", 
            "化学物质",
            "高温",
            "低温",
            "辐射",
            "振动",
            "紫外线",
            "电磁场",
            "缺氧"
        };
        
        System.out.println("名称\t\t拼音首字母\t五笔简码");
        System.out.println("------------------------------------");
        
        for (String factor : riskFactors) {
            try {
                String pinyinFirst = PinyinUtil.getFirstChars(factor);
                String wubiCode = WubiUtil.getWubiCode(factor);
                
                System.out.println(factor + "\t\t" + pinyinFirst + "\t\t" + wubiCode);
            } catch (Exception e) {
                System.err.println(factor + " 处理失败: " + e.getMessage());
            }
        }
    }
}
