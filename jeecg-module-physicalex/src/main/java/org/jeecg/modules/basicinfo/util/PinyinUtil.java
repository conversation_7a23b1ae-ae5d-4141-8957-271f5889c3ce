package org.jeecg.modules.basicinfo.util;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 拼音工具类 - 基于pinyin4j库
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V2.0
 */
@Slf4j
public class PinyinUtil {

    // 拼音输出格式配置
    private static final HanyuPinyinOutputFormat PINYIN_FORMAT = new HanyuPinyinOutputFormat();
    private static final HanyuPinyinOutputFormat FIRST_CHAR_FORMAT = new HanyuPinyinOutputFormat();

    static {
        // 配置完整拼音格式：小写、无音调、v表示ü
        PINYIN_FORMAT.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        PINYIN_FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        PINYIN_FORMAT.setVCharType(HanyuPinyinVCharType.WITH_V);

        // 配置首字母格式：大写、无音调、v表示ü
        FIRST_CHAR_FORMAT.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        FIRST_CHAR_FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        FIRST_CHAR_FORMAT.setVCharType(HanyuPinyinVCharType.WITH_V);
    }

    /**
     * 获取中文字符串的拼音首字母
     * @param chinese 中文字符串
     * @return 拼音首字母大写字符串
     */
    public static String getFirstChars(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            if (isChinese(c)) {
                // 中文字符，使用pinyin4j获取拼音首字母
                char firstChar = getPinyinFirstChar(c);
                result.append(firstChar);
            } else if (Character.isLetter(c)) {
                // 英文字符
                result.append(Character.toUpperCase(c));
            } else if (Character.isDigit(c)) {
                // 数字字符
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 使用pinyin4j获取汉字拼音首字母
     * @param c 汉字字符
     * @return 拼音首字母（大写）
     */
    private static char getPinyinFirstChar(char c) {
        try {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, FIRST_CHAR_FORMAT);
            if (pinyinArray != null && pinyinArray.length > 0 && pinyinArray[0].length() > 0) {
                return pinyinArray[0].charAt(0);
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            log.warn("获取汉字 '{}' 的拼音首字母失败: {}", c, e.getMessage());
        }
        // 如果获取失败，返回默认字符
        return 'Z';
    }

    /**
     * 获取中文字符串的全拼
     * @param chinese 中文字符串
     * @return 全拼小写字符串
     */
    public static String getFullPinyin(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            if (isChinese(c)) {
                // 中文字符，使用pinyin4j获取完整拼音
                String pinyin = getCharFullPinyin(c);
                result.append(pinyin);
            } else if (Character.isLetter(c)) {
                // 英文字符
                result.append(Character.toLowerCase(c));
            } else if (Character.isDigit(c)) {
                // 数字字符
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 使用pinyin4j获取汉字的完整拼音
     * @param c 汉字字符
     * @return 完整拼音（小写）
     */
    private static String getCharFullPinyin(char c) {
        try {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, PINYIN_FORMAT);
            if (pinyinArray != null && pinyinArray.length > 0) {
                return pinyinArray[0]; // 取第一个拼音（多音字情况）
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            log.warn("获取汉字 '{}' 的完整拼音失败: {}", c, e.getMessage());
        }
        // 如果获取失败，返回默认拼音
        return "unknown";
    }

    /**
     * 判断字符是否是中文
     * @param c 字符
     * @return 是否是中文字符
     */
    private static boolean isChinese(char c) {
        return c >= 0x4e00 && c <= 0x9fa5;
    }

    /**
     * 检查字符串是否包含中文
     * @param str 待检查的字符串
     * @return 是否包含中文字符
     */
    public static boolean containsChinese(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }

        for (char c : str.toCharArray()) {
            if (isChinese(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成智能助记码
     * 优先使用拼音首字母，如果没有中文则使用英文首字母
     */
    public static String generateSmartHelpChar(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }

        if (containsChinese(name)) {
            return getFirstChars(name);
        } else {
            // 英文或数字，直接提取首字母
            StringBuilder result = new StringBuilder();
            for (char c : name.toCharArray()) {
                if (Character.isLetter(c)) {
                    result.append(Character.toUpperCase(c));
                } else if (Character.isDigit(c)) {
                    result.append(c);
                }
            }
            return result.toString();
        }
    }

    /**
     * 生成智能全拼
     */
    public static String generateSmartPinyin(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }

        if (containsChinese(name)) {
            return getFullPinyin(name);
        } else {
            // 英文或数字，转为小写
            return name.toLowerCase().replaceAll("[^a-zA-Z0-9]", "");
        }
    }
}
